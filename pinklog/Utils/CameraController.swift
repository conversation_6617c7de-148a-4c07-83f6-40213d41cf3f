//
//  CameraController.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import AVFoundation
import SwiftUI

class CameraController: UIViewController {
    private var captureSession: AVCaptureSession?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?
    
    var onImageCaptured: ((UIImage) -> Void)?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
        setupUI()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        videoPreviewLayer?.frame = view.bounds
    }
    
    private func setupCamera() {
        captureSession = AVCaptureSession()
        guard let captureSession = captureSession else { return }
        
        // 配置会话质量
        captureSession.sessionPreset = .photo
        
        // 设置输入设备
        guard let backCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("无法访问后置摄像头")
            return
        }
        
        do {
            let input = try AVCaptureDeviceInput(device: backCamera)
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
            }
        } catch {
            print("相机输入设置错误: \(error)")
            return
        }
        
        // 设置照片输出
        photoOutput = AVCapturePhotoOutput()
        if let photoOutput = photoOutput, captureSession.canAddOutput(photoOutput) {
            captureSession.addOutput(photoOutput)
        }
        
        // 设置预览层
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer?.videoGravity = .resizeAspectFill
        videoPreviewLayer?.frame = view.bounds
        
        if let videoPreviewLayer = videoPreviewLayer {
            view.layer.addSublayer(videoPreviewLayer)
        }
        
        // 启动会话
        DispatchQueue.global(qos: .userInitiated).async {
            captureSession.startRunning()
        }
    }
    
    private func setupUI() {
        // 添加拍照按钮
        let captureButton = UIButton(type: .system)
        captureButton.backgroundColor = .white
        captureButton.layer.cornerRadius = 35
        captureButton.frame = CGRect(x: 0, y: 0, width: 70, height: 70)
        captureButton.center = CGPoint(x: view.center.x, y: view.bounds.height - 100)
        captureButton.addTarget(self, action: #selector(capturePhoto), for: .touchUpInside)
        
        // 添加拍照图标
        let cameraImage = UIImage(systemName: "camera.fill")
        captureButton.setImage(cameraImage, for: .normal)
        captureButton.tintColor = .black
        captureButton.imageView?.contentMode = .scaleAspectFit
        captureButton.contentVerticalAlignment = .fill
        captureButton.contentHorizontalAlignment = .fill
        captureButton.imageEdgeInsets = UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
        
        view.addSubview(captureButton)
        
        // 添加关闭按钮
        let closeButton = UIButton(type: .system)
        closeButton.setImage(UIImage(systemName: "xmark"), for: .normal)
        closeButton.tintColor = .white
        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        closeButton.layer.cornerRadius = 20
        closeButton.frame = CGRect(x: 30, y: 50, width: 40, height: 40)
        closeButton.addTarget(self, action: #selector(dismissCamera), for: .touchUpInside)
        view.addSubview(closeButton)
        
        // 添加切换到相册按钮
        let libraryButton = UIButton(type: .system)
        libraryButton.setImage(UIImage(systemName: "photo.on.rectangle"), for: .normal)
        libraryButton.tintColor = .white
        libraryButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        libraryButton.layer.cornerRadius = 20
        libraryButton.frame = CGRect(x: view.bounds.width - 70, y: 50, width: 40, height: 40)
        libraryButton.addTarget(self, action: #selector(openPhotoLibrary), for: .touchUpInside)
        view.addSubview(libraryButton)
    }
    
    @objc private func capturePhoto() {
        guard let photoOutput = photoOutput else { return }
        
        let photoSettings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: photoSettings, delegate: self)
    }
    
    @objc private func dismissCamera() {
        dismiss(animated: true)
    }
    
    @objc private func openPhotoLibrary() {
        // 这里可以触发相册选择
        // 暂时先关闭相机，让用户使用系统的PhotosPicker
        dismiss(animated: true)
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension CameraController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard error == nil else {
            print("拍照错误: \(error!)")
            return
        }
        
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            print("无法处理照片数据")
            return
        }
        
        DispatchQueue.main.async {
            self.onImageCaptured?(image)
            self.dismiss(animated: true)
        }
    }
}

// MARK: - SwiftUI集成
struct CameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> CameraController {
        let controller = CameraController()
        controller.onImageCaptured = { image in
            capturedImage = image
        }
        return controller
    }
    
    func updateUIViewController(_ uiViewController: CameraController, context: Context) {}
}
