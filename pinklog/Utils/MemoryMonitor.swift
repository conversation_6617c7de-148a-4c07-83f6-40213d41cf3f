//
//  MemoryMonitor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import Foundation
import UIKit

class MemoryMonitor {
    static let shared = MemoryMonitor()
    
    private init() {}
    
    /// 获取当前内存使用量（MB）
    func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0
        } else {
            return 0
        }
    }
    
    /// 检查内存使用是否过高
    func checkMemoryUsage() {
        let currentUsage = getCurrentMemoryUsage()
        print("📊 当前内存使用: \(String(format: "%.1f", currentUsage))MB")
        
        if currentUsage > 500 {
            print("⚠️ 内存使用过高: \(String(format: "%.1f", currentUsage))MB")
            
            // 触发内存清理
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .memoryWarning, object: nil)
            }
        }
    }
    
    /// 强制内存清理
    func forceMemoryCleanup() {
        print("🧹 执行内存清理...")
        
        // 清理图片缓存
        URLCache.shared.removeAllCachedResponses()
        
        // 触发垃圾回收
        autoreleasepool {
            // 空的autoreleasepool来触发清理
        }
        
        let afterUsage = getCurrentMemoryUsage()
        print("🧹 清理后内存使用: \(String(format: "%.1f", afterUsage))MB")
    }
}

extension Notification.Name {
    static let memoryWarning = Notification.Name("MemoryWarning")
}
