//
//  LazyImageLoader.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import Foundation
import UIKit
import CoreData

/// 按需加载产品图片的管理器，避免内存泄漏
class LazyImageLoader: ObservableObject {
    static let shared = LazyImageLoader()
    
    private var imageCache: [UUID: UIImage] = [:]
    private var loadingImages: Set<UUID> = []
    private let maxCacheSize = 20 // 最多缓存20张图片
    
    private init() {}
    
    /// 获取产品图片（带缓存）
    func getProductImage(productId: UUID, repository: ProductRepository) -> UIImage? {
        // 检查缓存
        if let cachedImage = imageCache[productId] {
            return cachedImage
        }
        
        // 避免重复加载
        if loadingImages.contains(productId) {
            return nil
        }
        
        // 开始加载
        loadingImages.insert(productId)
        
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            
            // 从数据库加载图片数据
            if let imageData = repository.fetchProductImage(productId: productId),
               let image = UIImage(data: imageData) {
                
                DispatchQueue.main.async {
                    // 缓存图片
                    self.cacheImage(image, for: productId)
                    self.loadingImages.remove(productId)
                    
                    // 通知UI更新
                    self.objectWillChange.send()
                }
            } else {
                DispatchQueue.main.async {
                    self.loadingImages.remove(productId)
                }
            }
        }
        
        return nil
    }
    
    /// 缓存图片
    private func cacheImage(_ image: UIImage, for productId: UUID) {
        // 如果缓存已满，移除最旧的图片
        if imageCache.count >= maxCacheSize {
            let oldestKey = imageCache.keys.first
            if let key = oldestKey {
                imageCache.removeValue(forKey: key)
            }
        }
        
        imageCache[productId] = image
        print("🖼️ 缓存产品图片: \(productId), 缓存大小: \(imageCache.count)")
    }
    
    /// 清理缓存
    func clearCache() {
        imageCache.removeAll()
        loadingImages.removeAll()
        print("🧹 清理图片缓存")
    }
    
    /// 预加载图片（可选）
    func preloadImage(productId: UUID, repository: ProductRepository) {
        if imageCache[productId] == nil && !loadingImages.contains(productId) {
            _ = getProductImage(productId: productId, repository: repository)
        }
    }
    
    /// 获取缓存状态
    func getCacheInfo() -> String {
        return "图片缓存: \(imageCache.count)/\(maxCacheSize), 加载中: \(loadingImages.count)"
    }
}
