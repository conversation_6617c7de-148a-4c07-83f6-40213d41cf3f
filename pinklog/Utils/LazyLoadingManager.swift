import Foundation
import SwiftUI
import CoreData

/// 惰性加载管理器，用于预加载和缓存视图
class LazyLoadingManager {
    // MARK: - 单例
    static let shared = LazyLoadingManager()
    
    // MARK: - 私有属性
    private var cachedViewModels: [String: Any] = [:]
    private var isPreloading = false
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 公共方法
    
    /// 预加载MemoirsViewModel
    /// - Parameter context: 托管对象上下文
    func preloadMemoirsViewModel(context: NSManagedObjectContext) {
        if !isPreloading && cachedViewModels["memoirs"] == nil {
            isPreloading = true
            
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                let viewModel = MemoirsViewModel(context: context)
                viewModel.loadAllStories()
                
                DispatchQueue.main.async {
                    self?.cachedViewModels["memoirs"] = viewModel
                    self?.isPreloading = false
                }
            }
        }
    }
    
    /// 获取缓存的MemoirsViewModel
    /// - Parameter context: 托管对象上下文
    /// - Returns: 缓存的MemoirsViewModel或创建新的
    func getMemoirsViewModel(context: NSManagedObjectContext) -> MemoirsViewModel {
        if let cachedViewModel = cachedViewModels["memoirs"] as? MemoirsViewModel {
            return cachedViewModel
        } else {
            let viewModel = MemoirsViewModel(context: context)
            cachedViewModels["memoirs"] = viewModel
            return viewModel
        }
    }
    
    /// 清除所有缓存
    func clearCache() {
        print("🧹 清理LazyLoadingManager缓存，释放内存...")
        cachedViewModels.removeAll()
        MemoryMonitor.shared.forceMemoryCleanup()
    }

    /// 检查缓存大小
    func getCacheInfo() -> String {
        return "缓存项目数: \(cachedViewModels.count)"
    }
}