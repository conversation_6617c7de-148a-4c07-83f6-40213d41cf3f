//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        let totalStartTime = CFAbsoluteTimeGetCurrent()
        print("🚀 开始快速创建贴纸风格")

        // 1. 快速提取主要颜色
        let dominantColor = extractMainColor(from: subjectImage)
        print("✅ 提取的颜色: \(dominantColor)")

        // 2. 快速创建贴纸效果
        let result = createSticker(subject: subjectImage, backgroundColor: dominantColor)

        let totalTime = CFAbsoluteTimeGetCurrent() - totalStartTime
        print("🎉 贴纸创建完成，总耗时: \(Int(totalTime * 1000))ms")

        return result
    }

    /// 快速提取图片主要颜色
    private func extractMainColor(from image: UIImage) -> UIColor {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 大幅缩小图片以提高速度
        let smallSize = CGSize(width: 32, height: 32)
        let renderer = UIGraphicsImageRenderer(size: smallSize)
        let smallImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: smallSize))
        }

        guard let cgImage = smallImage.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return UIColor.systemPink
        }

        var totalR: Int = 0
        var totalG: Int = 0
        var totalB: Int = 0
        var pixelCount: Int = 0

        let bytesPerPixel = 4
        let width = cgImage.width
        let height = cgImage.height

        // 只采样中心区域的每4个像素
        let centerX = width / 2
        let centerY = height / 2
        let radius = min(width, height) / 3

        for y in stride(from: 0, to: height, by: 2) {
            for x in stride(from: 0, to: width, by: 2) {
                // 检查是否在中心区域
                let dx = x - centerX
                let dy = y - centerY
                if dx * dx + dy * dy > radius * radius {
                    continue
                }

                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = bytes[pixelIndex + 3]

                if alpha > 100 {
                    let r = Int(bytes[pixelIndex])
                    let g = Int(bytes[pixelIndex + 1])
                    let b = Int(bytes[pixelIndex + 2])

                    // 简单亮度检查
                    if r + g + b > 150 {
                        totalR += r
                        totalG += g
                        totalB += b
                        pixelCount += 1
                    }
                }
            }
        }

        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        print("颜色提取耗时: \(Int(processingTime * 1000))ms")

        if pixelCount > 0 {
            let avgR = totalR / pixelCount
            let avgG = totalG / pixelCount
            let avgB = totalB / pixelCount

            print("快速提取颜色: R:\(avgR) G:\(avgG) B:\(avgB)")

            // 简化的粉色调整
            let finalColor = quickAdjustForPink(r: avgR, g: avgG, b: avgB)

            return UIColor(
                red: CGFloat(finalColor.r) / 255.0,
                green: CGFloat(finalColor.g) / 255.0,
                blue: CGFloat(finalColor.b) / 255.0,
                alpha: 1.0
            )
        }

        return UIColor.systemPink
    }

    /// 快速粉色调整
    private func quickAdjustForPink(r: Int, g: Int, b: Int) -> (r: Int, g: Int, b: Int) {
        // 简单检测粉色
        if r > g && r > 100 && b > 60 {
            let adjustedR = min(255, r + 20)
            let adjustedG = max(80, g - 10)
            let adjustedB = max(100, b - 15)
            print("粉色调整: R:\(adjustedR) G:\(adjustedG) B:\(adjustedB)")
            return (r: adjustedR, g: adjustedG, b: adjustedB)
        }
        return (r: r, g: g, b: b)
    }
    
    /// 超快速创建贴纸效果
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let stickerStartTime = CFAbsoluteTimeGetCurrent()

        let borderWidth: CGFloat = 20.0  // 减小边框以提高速度
        let finalSize = CGSize(
            width: subject.size.width + borderWidth * 2,
            height: subject.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: finalSize)

        let result = renderer.image { context in
            let cgContext = context.cgContext

            // 1. 快速填充背景
            cgContext.setFillColor(backgroundColor.cgColor)
            cgContext.fill(CGRect(origin: .zero, size: finalSize))

            let subjectRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: subject.size.width,
                height: subject.size.height
            )

            // 2. 超快速描边 - 使用简单偏移法
            let strokeWidth: CGFloat = 4.0
            cgContext.setFillColor(UIColor.white.cgColor)

            // 只用4个方向的偏移，大幅减少绘制次数
            let offsets: [(CGFloat, CGFloat)] = [
                (-strokeWidth, 0), (strokeWidth, 0),
                (0, -strokeWidth), (0, strokeWidth)
            ]

            for (dx, dy) in offsets {
                let offsetRect = CGRect(
                    x: subjectRect.origin.x + dx,
                    y: subjectRect.origin.y + dy,
                    width: subjectRect.width,
                    height: subjectRect.height
                )

                // 快速绘制白色轮廓
                if let cgImage = subject.cgImage {
                    cgContext.saveGState()
                    cgContext.clip(to: offsetRect, mask: cgImage)
                    cgContext.fill(offsetRect)
                    cgContext.restoreGState()
                }
            }

            // 3. 绘制主体
            subject.draw(in: subjectRect)
        }

        let stickerTime = CFAbsoluteTimeGetCurrent() - stickerStartTime
        print("贴纸创建耗时: \(Int(stickerTime * 1000))ms")

        return result
    }


}
