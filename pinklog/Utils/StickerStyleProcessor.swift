//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("开始创建贴纸风格")

        // 1. 直接从图片提取主要颜色
        let dominantColor = extractMainColor(from: subjectImage)
        print("提取的颜色: \(dominantColor)")

        // 2. 创建贴纸效果
        return createSticker(subject: subjectImage, backgroundColor: dominantColor)
    }

    /// 提取主要颜色
    private func extractMainColor(from image: UIImage) -> UIColor {
        guard let ciImage = CIImage(image: image),
              let filter = CIFilter(name: "CIAreaAverage") else {
            return UIColor.systemPink
        }

        // 分析图像中心区域
        let extent = ciImage.extent
        let centerRect = CGRect(
            x: extent.midX - extent.width * 0.3,
            y: extent.midY - extent.height * 0.3,
            width: extent.width * 0.6,
            height: extent.height * 0.6
        )

        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIVector(cgRect: centerRect), forKey: kCIInputExtentKey)

        guard let outputImage = filter.outputImage else {
            return UIColor.systemPink
        }

        let context = CIContext()
        var bitmap = [UInt8](repeating: 0, count: 4)

        context.render(
            outputImage,
            toBitmap: &bitmap,
            rowBytes: 4,
            bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
            format: .RGBA8,
            colorSpace: nil
        )

        var r = CGFloat(bitmap[0]) / 255.0
        var g = CGFloat(bitmap[1]) / 255.0
        var b = CGFloat(bitmap[2]) / 255.0

        // 粉色校正：减少紫色倾向
        if b > r * 0.75 && r > g {
            b = r * 0.65
            r = min(1.0, r * 1.05)
        }

        print("提取颜色: R:\(Int(r*255)) G:\(Int(g*255)) B:\(Int(b*255))")

        return UIColor(red: r, green: g, blue: b, alpha: 1.0)
    }
    
    /// 创建贴纸效果
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let borderWidth: CGFloat = 100.0
        let finalSize = CGSize(
            width: subject.size.width + borderWidth * 2,
            height: subject.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: finalSize)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 1. 绘制背景色
            cgContext.setFillColor(backgroundColor.cgColor)
            cgContext.fill(CGRect(origin: .zero, size: finalSize))

            // 2. 绘制带白色描边的主体图片
            let subjectRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: subject.size.width,
                height: subject.size.height
            )

            // 使用阴影创建白色描边效果
            cgContext.saveGState()

            // 设置白色阴影作为描边
            cgContext.setShadow(
                offset: CGSize.zero,
                blur: borderWidth,
                color: UIColor.white.cgColor
            )

            // 绘制图像，阴影就是描边
            subject.draw(in: subjectRect)

            cgContext.restoreGState()

            // 3. 再次绘制主体图片（清晰的主体）
            subject.draw(in: subjectRect)
        }
    }


}
