//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()
    
    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("开始创建贴纸风格，图片尺寸: \(subjectImage.size)")

        guard let ciImage = CIImage(image: subjectImage) else {
            print("无法创建CIImage")
            return nil
        }

        // 1. 给主体添加白色描边
        print("步骤1: 添加白色描边")
        guard let borderedSubject = addWhiteBorder(to: subjectImage) else {
            print("描边失败，返回原图")
            return subjectImage
        }

        // 2. 提取主体的主要颜色
        print("步骤2: 提取主要颜色")
        guard let dominantColor = extractDominantColor(from: ciImage) else {
            print("颜色提取失败，返回描边图片")
            return borderedSubject
        }

        // 3. 创建同色系渐变背景
        print("步骤3: 创建渐变背景")
        guard let backgroundImage = createGradientBackground(
            color: dominantColor,
            size: subjectImage.size
        ) else {
            print("背景创建失败，返回描边图片")
            return borderedSubject
        }

        // 4. 合成最终的贴纸
        print("步骤4: 合成最终贴纸")
        guard let finalSticker = compositeSticker(
            background: backgroundImage,
            subject: borderedSubject
        ) else {
            print("合成失败，返回描边图片")
            return borderedSubject
        }

        print("贴纸创建成功")
        return finalSticker
    }
    
    /// 提取图片的主要颜色
    private func extractDominantColor(from ciImage: CIImage) -> UIColor? {
        // 直接使用CIAreaAverage滤镜计算平均颜色，但排除透明区域
        guard let filter = CIFilter(name: "CIAreaAverage") else {
            print("无法创建CIAreaAverage滤镜")
            return nil
        }

        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIVector(cgRect: ciImage.extent), forKey: kCIInputExtentKey)

        guard let outputImage = filter.outputImage else {
            print("CIAreaAverage滤镜输出为空")
            return nil
        }

        // 创建1x1像素的上下文来获取平均颜色
        let context = CIContext()
        var bitmap = [UInt8](repeating: 0, count: 4)

        context.render(
            outputImage,
            toBitmap: &bitmap,
            rowBytes: 4,
            bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
            format: .RGBA8,
            colorSpace: nil
        )

        // 如果alpha值太低，说明主要是透明区域，尝试其他方法
        if bitmap[3] < 50 {
            print("Alpha值太低(\(bitmap[3]))，尝试采样方法")
            return extractColorBySampling(from: ciImage)
        }

        let color = UIColor(
            red: CGFloat(bitmap[0]) / 255.0,
            green: CGFloat(bitmap[1]) / 255.0,
            blue: CGFloat(bitmap[2]) / 255.0,
            alpha: 1.0
        )

        print("提取的主要颜色: R:\(bitmap[0]) G:\(bitmap[1]) B:\(bitmap[2]) A:\(bitmap[3])")
        return color
    }

    /// 通过采样方法提取颜色
    private func extractColorBySampling(from ciImage: CIImage) -> UIColor? {
        let context = CIContext()
        let extent = ciImage.extent

        // 采样图片中心区域的多个点
        let samplePoints = [
            CGPoint(x: extent.midX, y: extent.midY),
            CGPoint(x: extent.midX - extent.width * 0.1, y: extent.midY),
            CGPoint(x: extent.midX + extent.width * 0.1, y: extent.midY),
            CGPoint(x: extent.midX, y: extent.midY - extent.height * 0.1),
            CGPoint(x: extent.midX, y: extent.midY + extent.height * 0.1)
        ]

        var totalR: CGFloat = 0
        var totalG: CGFloat = 0
        var totalB: CGFloat = 0
        var validSamples = 0

        for point in samplePoints {
            var bitmap = [UInt8](repeating: 0, count: 4)
            context.render(
                ciImage,
                toBitmap: &bitmap,
                rowBytes: 4,
                bounds: CGRect(x: point.x, y: point.y, width: 1, height: 1),
                format: .RGBA8,
                colorSpace: nil
            )

            // 只计算非透明像素
            if bitmap[3] > 100 {
                totalR += CGFloat(bitmap[0])
                totalG += CGFloat(bitmap[1])
                totalB += CGFloat(bitmap[2])
                validSamples += 1
            }
        }

        guard validSamples > 0 else {
            print("没有找到有效的颜色样本")
            return UIColor.green // 默认返回绿色
        }

        let avgR = totalR / CGFloat(validSamples) / 255.0
        let avgG = totalG / CGFloat(validSamples) / 255.0
        let avgB = totalB / CGFloat(validSamples) / 255.0

        print("采样得到的平均颜色: R:\(avgR) G:\(avgG) B:\(avgB)")
        return UIColor(red: avgR, green: avgG, blue: avgB, alpha: 1.0)
    }

    
    /// 创建同色系渐变背景
    private func createGradientBackground(color: UIColor, size: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 创建渐变色
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

            print("背景颜色 - R:\(red) G:\(green) B:\(blue)")

            // 创建更饱和的颜色变化
            let lightColor = UIColor(
                red: min(1.0, red * 1.2 + 0.1),
                green: min(1.0, green * 1.2 + 0.1),
                blue: min(1.0, blue * 1.2 + 0.1),
                alpha: 1.0
            )

            let darkColor = UIColor(
                red: max(0.0, red * 0.7),
                green: max(0.0, green * 0.7),
                blue: max(0.0, blue * 0.7),
                alpha: 1.0
            )

            // 如果颜色太暗，使用更明亮的版本
            if red + green + blue < 0.3 {
                // 对于深色，创建更明显的渐变
                let brightColor = UIColor(
                    red: min(1.0, red + 0.4),
                    green: min(1.0, green + 0.4),
                    blue: min(1.0, blue + 0.4),
                    alpha: 1.0
                )

                let mediumColor = UIColor(
                    red: min(1.0, red + 0.2),
                    green: min(1.0, green + 0.2),
                    blue: min(1.0, blue + 0.2),
                    alpha: 1.0
                )

                // 创建径向渐变
                let colorSpace = CGColorSpaceCreateDeviceRGB()
                let colors = [brightColor.cgColor, mediumColor.cgColor, color.cgColor]
                let locations: [CGFloat] = [0.0, 0.5, 1.0]

                guard let gradient = CGGradient(
                    colorsSpace: colorSpace,
                    colors: colors as CFArray,
                    locations: locations
                ) else { return }

                let center = CGPoint(x: size.width / 2, y: size.height / 2)
                let radius = min(size.width, size.height) / 2

                cgContext.drawRadialGradient(
                    gradient,
                    startCenter: center,
                    startRadius: 0,
                    endCenter: center,
                    endRadius: radius,
                    options: []
                )
            } else {
                // 对于亮色，使用原来的逻辑
                let colorSpace = CGColorSpaceCreateDeviceRGB()
                let colors = [lightColor.cgColor, color.cgColor, darkColor.cgColor]
                let locations: [CGFloat] = [0.0, 0.5, 1.0]

                guard let gradient = CGGradient(
                    colorsSpace: colorSpace,
                    colors: colors as CFArray,
                    locations: locations
                ) else { return }

                let center = CGPoint(x: size.width / 2, y: size.height / 2)
                let radius = min(size.width, size.height) / 2

                cgContext.drawRadialGradient(
                    gradient,
                    startCenter: center,
                    startRadius: 0,
                    endCenter: center,
                    endRadius: radius,
                    options: []
                )
            }
        }
    }
    
    /// 给图片添加白色描边
    private func addWhiteBorder(to image: UIImage) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        // 方法1: 使用形态学操作创建描边
        let borderWidth: Float = 4.0 // 增加描边宽度

        // 1. 提取alpha通道
        guard let alphaFilter = CIFilter(name: "CIColorMatrix") else { return image }
        alphaFilter.setValue(ciImage, forKey: kCIInputImageKey)
        // 只保留alpha通道，其他通道设为0
        alphaFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputRVector")
        alphaFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputGVector")
        alphaFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputBVector")
        alphaFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputAVector")

        guard let alphaImage = alphaFilter.outputImage else { return image }

        // 2. 膨胀alpha通道创建描边区域
        guard let dilateFilter = CIFilter(name: "CIMorphologyMaximum") else { return image }
        dilateFilter.setValue(alphaImage, forKey: kCIInputImageKey)
        dilateFilter.setValue(borderWidth, forKey: kCIInputRadiusKey)

        guard let dilatedAlpha = dilateFilter.outputImage else { return image }

        // 3. 创建白色描边
        let whiteColor = CIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
        guard let whiteFilter = CIFilter(name: "CIConstantColorGenerator") else { return image }
        whiteFilter.setValue(whiteColor, forKey: kCIInputColorKey)

        guard let whiteImage = whiteFilter.outputImage else { return image }

        // 4. 使用膨胀的alpha作为蒙版应用白色
        guard let borderMaskFilter = CIFilter(name: "CIBlendWithMask") else { return image }
        borderMaskFilter.setValue(whiteImage.cropped(to: dilatedAlpha.extent), forKey: kCIInputImageKey)
        borderMaskFilter.setValue(dilatedAlpha, forKey: kCIInputMaskImageKey)
        borderMaskFilter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)

        guard let whiteBorder = borderMaskFilter.outputImage else { return image }

        // 5. 将原图叠加在白色描边上
        guard let compositeFilter = CIFilter(name: "CISourceOverCompositing") else { return image }
        compositeFilter.setValue(ciImage, forKey: kCIInputImageKey)
        compositeFilter.setValue(whiteBorder, forKey: kCIInputBackgroundImageKey)

        guard let finalImage = compositeFilter.outputImage else { return image }

        // 渲染为UIImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(finalImage, from: finalImage.extent) else {
            return image
        }

        print("白色描边处理完成")
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    /// 合成最终的贴纸
    private func compositeSticker(background: UIImage?, subject: UIImage) -> UIImage? {
        guard let background = background else { return subject }
        
        let renderer = UIGraphicsImageRenderer(size: background.size)
        
        return renderer.image { context in
            // 绘制背景
            background.draw(in: CGRect(origin: .zero, size: background.size))
            
            // 绘制主体（居中）
            let subjectRect = CGRect(
                x: (background.size.width - subject.size.width) / 2,
                y: (background.size.height - subject.size.height) / 2,
                width: subject.size.width,
                height: subject.size.height
            )
            
            subject.draw(in: subjectRect)
        }
    }
}
