//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()
    
    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        guard let ciImage = CIImage(image: subjectImage) else { return nil }
        
        // 1. 提取主体的主要颜色
        guard let dominantColor = extractDominantColor(from: ciImage) else {
            return addWhiteBorder(to: subjectImage)
        }
        
        // 2. 创建同色系渐变背景
        let backgroundImage = createGradientBackground(
            color: dominantColor,
            size: subjectImage.size
        )
        
        // 3. 给主体添加白色描边
        guard let borderedSubject = addWhiteBorder(to: subjectImage) else {
            return subjectImage
        }
        
        // 4. 合成最终的贴纸
        return compositeSticker(
            background: backgroundImage,
            subject: borderedSubject
        )
    }
    
    /// 提取图片的主要颜色
    private func extractDominantColor(from ciImage: CIImage) -> UIColor? {
        // 使用CIAreaAverage滤镜计算平均颜色
        guard let filter = CIFilter(name: "CIAreaAverage") else { return nil }
        
        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIVector(cgRect: ciImage.extent), forKey: kCIInputExtentKey)
        
        guard let outputImage = filter.outputImage else { return nil }
        
        // 创建1x1像素的上下文来获取平均颜色
        let context = CIContext()
        var bitmap = [UInt8](repeating: 0, count: 4)
        
        context.render(
            outputImage,
            toBitmap: &bitmap,
            rowBytes: 4,
            bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
            format: .RGBA8,
            colorSpace: nil
        )
        
        return UIColor(
            red: CGFloat(bitmap[0]) / 255.0,
            green: CGFloat(bitmap[1]) / 255.0,
            blue: CGFloat(bitmap[2]) / 255.0,
            alpha: 1.0
        )
    }
    
    /// 创建同色系渐变背景
    private func createGradientBackground(color: UIColor, size: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let cgContext = context.cgContext
            
            // 创建渐变色
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
            
            // 创建更亮和更暗的版本
            let lightColor = UIColor(
                red: min(1.0, red + 0.3),
                green: min(1.0, green + 0.3),
                blue: min(1.0, blue + 0.3),
                alpha: 1.0
            )
            
            let darkColor = UIColor(
                red: max(0.0, red - 0.2),
                green: max(0.0, green - 0.2),
                blue: max(0.0, blue - 0.2),
                alpha: 1.0
            )
            
            // 创建径向渐变
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let colors = [lightColor.cgColor, darkColor.cgColor]
            let locations: [CGFloat] = [0.0, 1.0]
            
            guard let gradient = CGGradient(
                colorsSpace: colorSpace,
                colors: colors as CFArray,
                locations: locations
            ) else { return }
            
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            let radius = min(size.width, size.height) / 2
            
            cgContext.drawRadialGradient(
                gradient,
                startCenter: center,
                startRadius: 0,
                endCenter: center,
                endRadius: radius,
                options: []
            )
        }
    }
    
    /// 给图片添加白色描边
    private func addWhiteBorder(to image: UIImage) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }
        
        // 创建形态学膨胀滤镜来扩展边缘
        guard let morphologyFilter = CIFilter(name: "CIMorphologyMaximum") else {
            return image
        }
        
        morphologyFilter.setValue(ciImage, forKey: kCIInputImageKey)
        morphologyFilter.setValue(3.0, forKey: kCIInputRadiusKey) // 描边宽度
        
        guard let expandedImage = morphologyFilter.outputImage else {
            return image
        }
        
        // 创建白色版本的扩展图像
        guard let colorFilter = CIFilter(name: "CIConstantColorGenerator") else {
            return image
        }
        
        colorFilter.setValue(CIColor.white, forKey: kCIInputColorKey)
        
        guard let whiteImage = colorFilter.outputImage else {
            return image
        }
        
        // 使用扩展的alpha通道作为蒙版
        guard let maskFilter = CIFilter(name: "CIBlendWithMask") else {
            return image
        }
        
        maskFilter.setValue(whiteImage.cropped(to: expandedImage.extent), forKey: kCIInputImageKey)
        maskFilter.setValue(expandedImage, forKey: kCIInputMaskImageKey)
        maskFilter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)
        
        guard let whiteBorderImage = maskFilter.outputImage else {
            return image
        }
        
        // 将原图叠加在白色描边上
        guard let compositeFilter = CIFilter(name: "CISourceOverCompositing") else {
            return image
        }
        
        compositeFilter.setValue(ciImage, forKey: kCIInputImageKey)
        compositeFilter.setValue(whiteBorderImage, forKey: kCIInputBackgroundImageKey)
        
        guard let finalImage = compositeFilter.outputImage else {
            return image
        }
        
        // 渲染为UIImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(finalImage, from: finalImage.extent) else {
            return image
        }
        
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    /// 合成最终的贴纸
    private func compositeSticker(background: UIImage?, subject: UIImage) -> UIImage? {
        guard let background = background else { return subject }
        
        let renderer = UIGraphicsImageRenderer(size: background.size)
        
        return renderer.image { context in
            // 绘制背景
            background.draw(in: CGRect(origin: .zero, size: background.size))
            
            // 绘制主体（居中）
            let subjectRect = CGRect(
                x: (background.size.width - subject.size.width) / 2,
                y: (background.size.height - subject.size.height) / 2,
                width: subject.size.width,
                height: subject.size.height
            )
            
            subject.draw(in: subjectRect)
        }
    }
}
