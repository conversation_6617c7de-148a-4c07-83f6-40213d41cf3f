//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("开始创建贴纸风格")

        // 1. 直接从图片提取主要颜色
        let dominantColor = extractMainColor(from: subjectImage)
        print("提取的颜色: \(dominantColor)")

        // 2. 创建贴纸效果
        return createSticker(subject: subjectImage, backgroundColor: dominantColor)
    }

    /// 提取图片主要颜色的改进方法
    private func extractMainColor(from image: UIImage) -> UIColor {
        // 缩小图片以提高处理速度
        let smallSize = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: smallSize)
        let smallImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: smallSize))
        }

        guard let cgImage = smallImage.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return UIColor.systemPink // 默认粉色
        }

        // 收集颜色样本，排除边缘和暗色
        var colorSamples: [(r: Int, g: Int, b: Int, brightness: Double)] = []

        let bytesPerPixel = 4
        let width = cgImage.width
        let height = cgImage.height

        // 只分析中心区域，避免边缘阴影影响
        let centerX = width / 2
        let centerY = height / 2
        let sampleRadius = min(width, height) / 3

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = bytes[pixelIndex + 3]

                // 只处理非透明像素
                if alpha > 150 {
                    let r = Int(bytes[pixelIndex])
                    let g = Int(bytes[pixelIndex + 1])
                    let b = Int(bytes[pixelIndex + 2])

                    // 计算亮度
                    let brightness = (0.299 * Double(r) + 0.587 * Double(g) + 0.114 * Double(b)) / 255.0

                    // 排除太暗的像素（可能是阴影）
                    if brightness > 0.2 {
                        // 优先采样中心区域
                        let distance = sqrt(Double((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY)))
                        if distance <= Double(sampleRadius) {
                            colorSamples.append((r: r, g: g, b: b, brightness: brightness))
                        }
                    }
                }
            }
        }

        // 如果中心区域样本不够，扩大范围
        if colorSamples.count < 50 {
            for y in 0..<height {
                for x in 0..<width {
                    let pixelIndex = (y * width + x) * bytesPerPixel
                    let alpha = bytes[pixelIndex + 3]

                    if alpha > 150 {
                        let r = Int(bytes[pixelIndex])
                        let g = Int(bytes[pixelIndex + 1])
                        let b = Int(bytes[pixelIndex + 2])

                        let brightness = (0.299 * Double(r) + 0.587 * Double(g) + 0.114 * Double(b)) / 255.0

                        if brightness > 0.2 {
                            colorSamples.append((r: r, g: g, b: b, brightness: brightness))
                        }
                    }
                }
            }
        }

        guard !colorSamples.isEmpty else {
            print("没有找到有效颜色样本，使用默认粉色")
            return UIColor.systemPink
        }

        // 按亮度排序，取中等亮度的颜色（避免过亮或过暗）
        let sortedSamples = colorSamples.sorted { $0.brightness < $1.brightness }
        let middleIndex = sortedSamples.count / 2
        let sampleRange = max(1, sortedSamples.count / 4)

        let startIndex = max(0, middleIndex - sampleRange / 2)
        let endIndex = min(sortedSamples.count, middleIndex + sampleRange / 2)

        let selectedSamples = Array(sortedSamples[startIndex..<endIndex])

        // 计算平均颜色
        let avgR = selectedSamples.map { $0.r }.reduce(0, +) / selectedSamples.count
        let avgG = selectedSamples.map { $0.g }.reduce(0, +) / selectedSamples.count
        let avgB = selectedSamples.map { $0.b }.reduce(0, +) / selectedSamples.count

        print("改进算法提取颜色:")
        print("- 总样本数: \(colorSamples.count)")
        print("- 选中样本数: \(selectedSamples.count)")
        print("- 平均颜色: R:\(avgR) G:\(avgG) B:\(avgB)")

        // 对粉色进行特殊处理
        let finalColor = adjustColorForPink(r: avgR, g: avgG, b: avgB)

        return UIColor(
            red: CGFloat(finalColor.r) / 255.0,
            green: CGFloat(finalColor.g) / 255.0,
            blue: CGFloat(finalColor.b) / 255.0,
            alpha: 1.0
        )
    }

    /// 对粉色进行特殊调整
    private func adjustColorForPink(r: Int, g: Int, b: Int) -> (r: Int, g: Int, b: Int) {
        // 检测是否为粉色系（红色分量较高，蓝色分量中等，绿色分量较低）
        if r > g && r > 100 && b > 80 && b < r {
            // 增强粉色特征
            let enhancedR = min(255, Int(Double(r) * 1.1))
            let reducedG = max(50, Int(Double(g) * 0.9))
            let adjustedB = min(200, Int(Double(b) * 0.95))

            print("- 检测到粉色，调整为: R:\(enhancedR) G:\(reducedG) B:\(adjustedB)")
            return (r: enhancedR, g: reducedG, b: adjustedB)
        }

        return (r: r, g: g, b: b)
    }
    
    /// 创建贴纸效果
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let borderWidth: CGFloat = 100.0
        let finalSize = CGSize(
            width: subject.size.width + borderWidth * 2,
            height: subject.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: finalSize)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 1. 绘制背景色
            cgContext.setFillColor(backgroundColor.cgColor)
            cgContext.fill(CGRect(origin: .zero, size: finalSize))

            // 2. 绘制带白色描边的主体图片
            let subjectRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: subject.size.width,
                height: subject.size.height
            )

            // 使用阴影创建白色描边效果
            cgContext.saveGState()

            // 设置白色阴影作为描边
            cgContext.setShadow(
                offset: CGSize.zero,
                blur: borderWidth,
                color: UIColor.white.cgColor
            )

            // 绘制图像，阴影就是描边
            subject.draw(in: subjectRect)

            cgContext.restoreGState()

            // 3. 再次绘制主体图片（清晰的主体）
            subject.draw(in: subjectRect)
        }
    }


}
