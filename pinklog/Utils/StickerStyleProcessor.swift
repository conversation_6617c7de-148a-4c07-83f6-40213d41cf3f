//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("开始创建贴纸风格")

        // 1. 直接从图片提取主要颜色
        let dominantColor = extractMainColor(from: subjectImage)
        print("提取的颜色: \(dominantColor)")

        // 2. 创建贴纸效果
        return createSticker(subject: subjectImage, backgroundColor: dominantColor)
    }

    /// 使用iOS原生方法提取主要颜色
    private func extractMainColor(from image: UIImage) -> UIColor {
        print("🎨 开始提取主要颜色...")

        // 方法1: 使用Core Image的CIAreaAverage（最准确）
        if let ciColor = extractColorWithCoreImage(from: image) {
            print("✅ Core Image方法成功: \(ciColor)")
            return ciColor
        }

        // 方法2: 简单中心采样（备用）
        if let centerColor = extractCenterColor(from: image) {
            print("✅ 中心采样方法成功: \(centerColor)")
            return centerColor
        }

        // 方法3: 预设颜色（最后备用）
        print("⚠️ 使用预设粉色")
        return UIColor.systemPink
    }

    /// 使用Core Image提取平均颜色
    private func extractColorWithCoreImage(from image: UIImage) -> UIColor? {
        guard let ciImage = CIImage(image: image) else {
            print("❌ 无法创建CIImage")
            return nil
        }

        // 只分析图像中心区域，避免边缘影响
        let extent = ciImage.extent
        let centerRect = CGRect(
            x: extent.midX - extent.width * 0.25,
            y: extent.midY - extent.height * 0.25,
            width: extent.width * 0.5,
            height: extent.height * 0.5
        )

        guard let filter = CIFilter(name: "CIAreaAverage") else {
            print("❌ 无法创建CIAreaAverage滤镜")
            return nil
        }

        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIVector(cgRect: centerRect), forKey: kCIInputExtentKey)

        guard let outputImage = filter.outputImage else {
            print("❌ 滤镜输出为空")
            return nil
        }

        let context = CIContext()
        var bitmap = [UInt8](repeating: 0, count: 4)

        context.render(
            outputImage,
            toBitmap: &bitmap,
            rowBytes: 4,
            bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
            format: .RGBA8,
            colorSpace: nil
        )

        let r = CGFloat(bitmap[0]) / 255.0
        let g = CGFloat(bitmap[1]) / 255.0
        let b = CGFloat(bitmap[2]) / 255.0

        print("🎨 Core Image提取颜色: R:\(bitmap[0]) G:\(bitmap[1]) B:\(bitmap[2])")

        // 颜色校正：如果是粉色系，增强粉色特征
        let correctedColor = correctPinkColor(r: r, g: g, b: b)

        return UIColor(red: correctedColor.r, green: correctedColor.g, blue: correctedColor.b, alpha: 1.0)
    }

    /// 简单的中心区域颜色提取
    private func extractCenterColor(from image: UIImage) -> UIColor? {
        let size = CGSize(width: 50, height: 50)
        let renderer = UIGraphicsImageRenderer(size: size)
        let smallImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }

        guard let cgImage = smallImage.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return nil
        }

        // 只取中心9个像素的平均值
        let centerPixels = [
            (24, 24), (25, 24), (26, 24),
            (24, 25), (25, 25), (26, 25),
            (24, 26), (25, 26), (26, 26)
        ]

        var totalR = 0, totalG = 0, totalB = 0, count = 0

        for (x, y) in centerPixels {
            let pixelIndex = (y * 50 + x) * 4
            let alpha = bytes[pixelIndex + 3]

            if alpha > 100 {
                totalR += Int(bytes[pixelIndex])
                totalG += Int(bytes[pixelIndex + 1])
                totalB += Int(bytes[pixelIndex + 2])
                count += 1
            }
        }

        guard count > 0 else { return nil }

        let avgR = CGFloat(totalR) / CGFloat(count) / 255.0
        let avgG = CGFloat(totalG) / CGFloat(count) / 255.0
        let avgB = CGFloat(totalB) / CGFloat(count) / 255.0

        print("🎨 中心采样颜色: R:\(totalR/count) G:\(totalG/count) B:\(totalB/count)")

        let correctedColor = correctPinkColor(r: avgR, g: avgG, b: avgB)
        return UIColor(red: correctedColor.r, green: correctedColor.g, blue: correctedColor.b, alpha: 1.0)
    }

    /// 粉色校正
    private func correctPinkColor(r: CGFloat, g: CGFloat, b: CGFloat) -> (r: CGFloat, g: CGFloat, b: CGFloat) {
        // 如果蓝色分量过高（导致紫色），减少蓝色
        if b > r * 0.8 && r > g {
            let correctedB = min(b, r * 0.7)
            let correctedR = min(1.0, r * 1.1)
            print("🔧 粉色校正: 减少蓝色分量 \(Int(b*255)) -> \(Int(correctedB*255))")
            return (r: correctedR, g: g, b: correctedB)
        }

        return (r: r, g: g, b: b)
    }
    
    /// 创建贴纸效果
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let borderWidth: CGFloat = 100.0
        let finalSize = CGSize(
            width: subject.size.width + borderWidth * 2,
            height: subject.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: finalSize)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 1. 绘制背景色
            cgContext.setFillColor(backgroundColor.cgColor)
            cgContext.fill(CGRect(origin: .zero, size: finalSize))

            // 2. 绘制带白色描边的主体图片
            let subjectRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: subject.size.width,
                height: subject.size.height
            )

            // 使用阴影创建白色描边效果
            cgContext.saveGState()

            // 设置白色阴影作为描边
            cgContext.setShadow(
                offset: CGSize.zero,
                blur: borderWidth,
                color: UIColor.white.cgColor
            )

            // 绘制图像，阴影就是描边
            subject.draw(in: subjectRect)

            cgContext.restoreGState()

            // 3. 再次绘制主体图片（清晰的主体）
            subject.draw(in: subjectRect)
        }
    }


}
