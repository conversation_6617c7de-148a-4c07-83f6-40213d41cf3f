//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()
    
    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("🎨 开始创建贴纸风格，图片尺寸: \(subjectImage.size)")

        // 先检查图片是否有透明背景
        let hasTransparency = checkImageTransparency(subjectImage)
        print("🔍 图片透明度检查: \(hasTransparency ? "有透明背景" : "无透明背景")")

        guard let ciImage = CIImage(image: subjectImage) else {
            print("❌ 无法创建CIImage")
            return nil
        }

        // 1. 提取主体的主要颜色
        print("🎨 步骤1: 提取主要颜色")
        let dominantColor = extractDominantColor(from: ciImage) ?? UIColor.green
        print("✅ 提取到颜色")

        // 2. 创建同色系渐变背景
        print("🌈 步骤2: 创建渐变背景")
        guard let backgroundImage = createGradientBackground(
            color: dominantColor,
            size: subjectImage.size
        ) else {
            print("❌ 背景创建失败")
            return subjectImage
        }
        print("✅ 背景创建成功")

        // 3. 给主体添加白色描边
        print("⚪ 步骤3: 添加白色描边")
        let borderedSubject = addWhiteBorder(to: subjectImage) ?? subjectImage
        print("✅ 描边处理完成")

        // 4. 合成最终的贴纸
        print("🔧 步骤4: 合成最终贴纸")
        guard let finalSticker = compositeSticker(
            background: backgroundImage,
            subject: borderedSubject
        ) else {
            print("❌ 合成失败")
            return borderedSubject
        }

        print("🎉 贴纸创建成功！")
        return finalSticker
    }

    /// 检查图片是否有透明背景
    private func checkImageTransparency(_ image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else { return false }

        let alphaInfo = cgImage.alphaInfo
        return alphaInfo == .first || alphaInfo == .last ||
               alphaInfo == .premultipliedFirst || alphaInfo == .premultipliedLast
    }
    
    /// 提取图片的主要颜色
    private func extractDominantColor(from ciImage: CIImage) -> UIColor? {
        print("🔍 开始提取主要颜色")

        // 方法1: 使用采样方法提取颜色（更可靠）
        let sampledColor = extractColorBySampling(from: ciImage)
        if let color = sampledColor {
            print("✅ 采样方法成功提取颜色")
            return color
        }

        // 方法2: 使用CIAreaAverage作为备用
        guard let filter = CIFilter(name: "CIAreaAverage") else {
            print("❌ 无法创建CIAreaAverage滤镜，使用默认绿色")
            return UIColor.green
        }

        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIVector(cgRect: ciImage.extent), forKey: kCIInputExtentKey)

        guard let outputImage = filter.outputImage else {
            print("❌ CIAreaAverage滤镜输出为空，使用默认绿色")
            return UIColor.green
        }

        let context = CIContext()
        var bitmap = [UInt8](repeating: 0, count: 4)

        context.render(
            outputImage,
            toBitmap: &bitmap,
            rowBytes: 4,
            bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
            format: .RGBA8,
            colorSpace: nil
        )

        let color = UIColor(
            red: CGFloat(bitmap[0]) / 255.0,
            green: CGFloat(bitmap[1]) / 255.0,
            blue: CGFloat(bitmap[2]) / 255.0,
            alpha: 1.0
        )

        print("🎨 CIAreaAverage提取的颜色: R:\(bitmap[0]) G:\(bitmap[1]) B:\(bitmap[2]) A:\(bitmap[3])")
        return color
    }

    /// 通过采样方法提取颜色
    private func extractColorBySampling(from ciImage: CIImage) -> UIColor? {
        print("🔬 开始采样提取颜色")
        let context = CIContext()
        let extent = ciImage.extent

        // 扩大采样范围，包含更多区域
        let samplePoints = [
            // 中心区域
            CGPoint(x: extent.midX, y: extent.midY),
            // 四个象限的中心点
            CGPoint(x: extent.midX - extent.width * 0.25, y: extent.midY - extent.height * 0.25),
            CGPoint(x: extent.midX + extent.width * 0.25, y: extent.midY - extent.height * 0.25),
            CGPoint(x: extent.midX - extent.width * 0.25, y: extent.midY + extent.height * 0.25),
            CGPoint(x: extent.midX + extent.width * 0.25, y: extent.midY + extent.height * 0.25),
            // 边缘中点
            CGPoint(x: extent.midX, y: extent.midY - extent.height * 0.3),
            CGPoint(x: extent.midX, y: extent.midY + extent.height * 0.3),
            CGPoint(x: extent.midX - extent.width * 0.3, y: extent.midY),
            CGPoint(x: extent.midX + extent.width * 0.3, y: extent.midY)
        ]

        var colorSamples: [(r: CGFloat, g: CGFloat, b: CGFloat)] = []

        for (index, point) in samplePoints.enumerated() {
            var bitmap = [UInt8](repeating: 0, count: 4)
            context.render(
                ciImage,
                toBitmap: &bitmap,
                rowBytes: 4,
                bounds: CGRect(x: point.x, y: point.y, width: 1, height: 1),
                format: .RGBA8,
                colorSpace: nil
            )

            // 只计算非透明像素（alpha > 50）
            if bitmap[3] > 50 {
                let r = CGFloat(bitmap[0]) / 255.0
                let g = CGFloat(bitmap[1]) / 255.0
                let b = CGFloat(bitmap[2]) / 255.0
                colorSamples.append((r: r, g: g, b: b))
                print("📍 采样点\(index): R:\(bitmap[0]) G:\(bitmap[1]) B:\(bitmap[2]) A:\(bitmap[3])")
            }
        }

        guard !colorSamples.isEmpty else {
            print("❌ 没有找到有效的颜色样本，使用默认绿色")
            return nil
        }

        // 计算平均颜色
        let avgR = colorSamples.map { $0.r }.reduce(0, +) / CGFloat(colorSamples.count)
        let avgG = colorSamples.map { $0.g }.reduce(0, +) / CGFloat(colorSamples.count)
        let avgB = colorSamples.map { $0.b }.reduce(0, +) / CGFloat(colorSamples.count)

        print("🎨 采样得到的平均颜色: R:\(Int(avgR*255)) G:\(Int(avgG*255)) B:\(Int(avgB*255))")
        print("🎨 有效采样点数: \(colorSamples.count)/\(samplePoints.count)")

        return UIColor(red: avgR, green: avgG, blue: avgB, alpha: 1.0)
    }

    
    /// 创建同色系渐变背景
    private func createGradientBackground(color: UIColor, size: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 创建渐变色
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

            print("背景颜色 - R:\(red) G:\(green) B:\(blue)")

            // 创建更饱和的颜色变化
            let lightColor = UIColor(
                red: min(1.0, red * 1.2 + 0.1),
                green: min(1.0, green * 1.2 + 0.1),
                blue: min(1.0, blue * 1.2 + 0.1),
                alpha: 1.0
            )

            let darkColor = UIColor(
                red: max(0.0, red * 0.7),
                green: max(0.0, green * 0.7),
                blue: max(0.0, blue * 0.7),
                alpha: 1.0
            )

            // 如果颜色太暗，使用更明亮的版本
            if red + green + blue < 0.3 {
                // 对于深色，创建更明显的渐变
                let brightColor = UIColor(
                    red: min(1.0, red + 0.4),
                    green: min(1.0, green + 0.4),
                    blue: min(1.0, blue + 0.4),
                    alpha: 1.0
                )

                let mediumColor = UIColor(
                    red: min(1.0, red + 0.2),
                    green: min(1.0, green + 0.2),
                    blue: min(1.0, blue + 0.2),
                    alpha: 1.0
                )

                // 创建径向渐变
                let colorSpace = CGColorSpaceCreateDeviceRGB()
                let colors = [brightColor.cgColor, mediumColor.cgColor, color.cgColor]
                let locations: [CGFloat] = [0.0, 0.5, 1.0]

                guard let gradient = CGGradient(
                    colorsSpace: colorSpace,
                    colors: colors as CFArray,
                    locations: locations
                ) else { return }

                let center = CGPoint(x: size.width / 2, y: size.height / 2)
                let radius = min(size.width, size.height) / 2

                cgContext.drawRadialGradient(
                    gradient,
                    startCenter: center,
                    startRadius: 0,
                    endCenter: center,
                    endRadius: radius,
                    options: []
                )
            } else {
                // 对于亮色，使用原来的逻辑
                let colorSpace = CGColorSpaceCreateDeviceRGB()
                let colors = [lightColor.cgColor, color.cgColor, darkColor.cgColor]
                let locations: [CGFloat] = [0.0, 0.5, 1.0]

                guard let gradient = CGGradient(
                    colorsSpace: colorSpace,
                    colors: colors as CFArray,
                    locations: locations
                ) else { return }

                let center = CGPoint(x: size.width / 2, y: size.height / 2)
                let radius = min(size.width, size.height) / 2

                cgContext.drawRadialGradient(
                    gradient,
                    startCenter: center,
                    startRadius: 0,
                    endCenter: center,
                    endRadius: radius,
                    options: []
                )
            }
        }
    }
    
    /// 给图片添加白色描边
    private func addWhiteBorder(to image: UIImage) -> UIImage? {
        print("⚪ 开始添加白色描边")

        // 使用Core Graphics直接绘制描边，更可靠
        let borderWidth: CGFloat = 6.0
        let newSize = CGSize(
            width: image.size.width + borderWidth * 2,
            height: image.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: newSize)

        let borderedImage = renderer.image { context in
            let cgContext = context.cgContext

            // 设置白色描边
            cgContext.setStrokeColor(UIColor.white.cgColor)
            cgContext.setLineWidth(borderWidth)
            cgContext.setLineJoin(.round)
            cgContext.setLineCap(.round)

            // 绘制原图多次来创建描边效果
            let imageRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: image.size.width,
                height: image.size.height
            )

            // 先绘制白色背景作为描边
            for angle in stride(from: 0.0, to: 2 * Double.pi, by: Double.pi / 4) {
                let offsetX = cos(angle) * Double(borderWidth / 2)
                let offsetY = sin(angle) * Double(borderWidth / 2)

                let offsetRect = CGRect(
                    x: imageRect.origin.x + CGFloat(offsetX),
                    y: imageRect.origin.y + CGFloat(offsetY),
                    width: imageRect.width,
                    height: imageRect.height
                )

                // 使用白色绘制偏移的图像作为描边
                cgContext.saveGState()
                cgContext.setBlendMode(.normal)
                cgContext.setFillColor(UIColor.white.cgColor)

                // 创建图像的路径并描边
                if let cgImage = image.cgImage {
                    cgContext.draw(cgImage, in: offsetRect)
                    cgContext.setBlendMode(.sourceIn)
                    cgContext.fill(offsetRect)
                }
                cgContext.restoreGState()
            }

            // 最后绘制原图
            image.draw(in: imageRect)
        }

        print("✅ 白色描边添加完成")
        return borderedImage
    }
    
    /// 合成最终的贴纸
    private func compositeSticker(background: UIImage?, subject: UIImage) -> UIImage? {
        guard let background = background else { return subject }
        
        let renderer = UIGraphicsImageRenderer(size: background.size)
        
        return renderer.image { context in
            // 绘制背景
            background.draw(in: CGRect(origin: .zero, size: background.size))
            
            // 绘制主体（居中）
            let subjectRect = CGRect(
                x: (background.size.width - subject.size.width) / 2,
                y: (background.size.height - subject.size.height) / 2,
                width: subject.size.width,
                height: subject.size.height
            )
            
            subject.draw(in: subjectRect)
        }
    }
}
