//
//  StickerStyleProcessor.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import CoreImage
import CoreGraphics

class StickerStyleProcessor {
    static let shared = StickerStyleProcessor()

    private init() {}
    
    /// 将抠图结果转换为贴纸风格
    /// - Parameter subjectImage: 抠出的主体图片（带透明背景）
    /// - Returns: 贴纸风格的图片
    func createStickerStyle(from subjectImage: UIImage) -> UIImage? {
        print("开始创建贴纸风格")

        // 1. 直接从图片提取主要颜色
        let dominantColor = extractMainColor(from: subjectImage)
        print("提取的颜色: \(dominantColor)")

        // 2. 创建贴纸效果
        return createSticker(subject: subjectImage, backgroundColor: dominantColor)
    }

    /// 提取图片主要颜色的简单方法
    private func extractMainColor(from image: UIImage) -> UIColor {
        // 缩小图片以提高处理速度
        let smallSize = CGSize(width: 50, height: 50)
        let renderer = UIGraphicsImageRenderer(size: smallSize)
        let smallImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: smallSize))
        }

        guard let cgImage = smallImage.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return UIColor.green // 默认绿色
        }

        var totalR: Int = 0
        var totalG: Int = 0
        var totalB: Int = 0
        var pixelCount: Int = 0

        let bytesPerPixel = 4
        let width = cgImage.width
        let height = cgImage.height

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = bytes[pixelIndex + 3]

                // 只处理非透明像素
                if alpha > 100 {
                    totalR += Int(bytes[pixelIndex])
                    totalG += Int(bytes[pixelIndex + 1])
                    totalB += Int(bytes[pixelIndex + 2])
                    pixelCount += 1
                }
            }
        }

        if pixelCount > 0 {
            let avgR = CGFloat(totalR) / CGFloat(pixelCount) / 255.0
            let avgG = CGFloat(totalG) / CGFloat(pixelCount) / 255.0
            let avgB = CGFloat(totalB) / CGFloat(pixelCount) / 255.0

            print("平均颜色: R:\(Int(avgR*255)) G:\(Int(avgG*255)) B:\(Int(avgB*255))")
            return UIColor(red: avgR, green: avgG, blue: avgB, alpha: 1.0)
        }

        return UIColor.green // 默认绿色
    }
    
    /// 创建贴纸效果
    private func createSticker(subject: UIImage, backgroundColor: UIColor) -> UIImage? {
        let borderWidth: CGFloat = 50.0
        let finalSize = CGSize(
            width: subject.size.width + borderWidth * 2,
            height: subject.size.height + borderWidth * 2
        )

        let renderer = UIGraphicsImageRenderer(size: finalSize)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 1. 绘制背景色
            cgContext.setFillColor(backgroundColor.cgColor)
            cgContext.fill(CGRect(origin: .zero, size: finalSize))

            // 2. 绘制带白色描边的主体图片
            let subjectRect = CGRect(
                x: borderWidth,
                y: borderWidth,
                width: subject.size.width,
                height: subject.size.height
            )

            // 使用阴影创建白色描边效果
            cgContext.saveGState()

            // 设置白色阴影作为描边
            cgContext.setShadow(
                offset: CGSize.zero,
                blur: borderWidth,
                color: UIColor.white.cgColor
            )

            // 绘制图像，阴影就是描边
            subject.draw(in: subjectRect)

            cgContext.restoreGState()

            // 3. 再次绘制主体图片（清晰的主体）
            subject.draw(in: subjectRect)
        }
    }


}
