import Foundation
import CoreData
import Combine

// MARK: - 通用仓库协议
protocol Repository {
    associatedtype Entity

    func getAll() -> [Entity]
    func getById(id: UUID) -> Entity?
    func save(entity: Entity) -> Bool
    func delete(entity: Entity) -> Bool
}

// MARK: - CoreData仓库基类
class CoreDataRepository<T: NSManagedObject> {
    private let context: NSManagedObjectContext

    init(context: NSManagedObjectContext) {
        self.context = context
    }

    // 获取上下文
    func getContext() -> NSManagedObjectContext {
        return context
    }

    // 获取所有实体
    func fetchAll(sortDescriptors: [NSSortDescriptor]? = nil) -> [T] {
        let request = T.fetchRequest()
        request.sortDescriptors = sortDescriptors
        // 🚨 移除refreshAllObjects以避免内存泄漏
        // context.refreshAllObjects()

        do {
            let results = try context.fetch(request) as? [T] ?? []
            print("📊 fetchAll(\(T.self)): 获取\(results.count)条记录，内存: \(String(format: "%.1f", MemoryMonitor.shared.getCurrentMemoryUsage()))MB")
            return results
        } catch {
            print("Error fetching \(T.self): \(error)")
            return []
        }
    }

    // 获取特定ID的实体
    func fetchById(id: UUID) -> T? {
        let request = T.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", id as CVarArg)

        do {
            let results = try context.fetch(request) as? [T]
            return results?.first
        } catch {
            print("Error fetching \(T.self) with id \(id): \(error)")
            return nil
        }
    }

    // 根据谓词获取实体
    func fetch(predicate: NSPredicate?, sortDescriptors: [NSSortDescriptor]? = nil) -> [T] {
        let request = T.fetchRequest()
        request.predicate = predicate
        request.sortDescriptors = sortDescriptors

        do {
            return try context.fetch(request) as? [T] ?? []
        } catch {
            print("Error fetching \(T.self) with predicate: \(error)")
            return []
        }
    }

    // 保存实体
    func save(block: @escaping (NSManagedObjectContext) -> Void) -> Bool {
        block(context)

        do {
            try context.save()
            return true
        } catch {
            print("Error saving context: \(error)")
            context.rollback()
            return false
        }
    }

    // 删除实体
    func delete(entity: T) -> Bool {
        context.delete(entity)

        do {
            try context.save()
            return true
        } catch {
            print("Error deleting \(T.self): \(error)")
            context.rollback()
            return false
        }
    }

    // 批量删除实体
    func deleteMultiple(entities: [T]) -> Bool {
        entities.forEach { context.delete($0) }

        do {
            try context.save()
            return true
        } catch {
            print("Error batch deleting \(T.self): \(error)")
            context.rollback()
            return false
        }
    }
}

// MARK: - 产品仓库
class ProductRepository: CoreDataRepository<Product> {

    // 获取产品列表（不包含图片数据，避免内存泄漏）
    func fetchAllWithoutImages() -> [Product] {
        let request = Product.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(key: "purchaseDate", ascending: false)]

        // 🚨 关键修复：排除图片数据，只获取基本信息
        request.propertiesToFetch = [
            "id", "name", "brand", "model", "price", "purchaseDate",
            "initialSatisfaction", "valuationMethod", "expectedLifespan",
            "purchaseMotivation", "purchaseNotes", "warrantyEndDate",
            "expiryDate", "quantity", "expectedUsageFrequency"
        ]
        request.returnsObjectsAsFaults = false

        do {
            let results = try getContext().fetch(request)
            print("📦 获取产品列表(无图片): \(results.count)条记录，内存: \(String(format: "%.1f", MemoryMonitor.shared.getCurrentMemoryUsage()))MB")
            return results
        } catch {
            print("Error fetching products without images: \(error)")
            return []
        }
    }

    // 单独获取产品图片（按需加载）
    func fetchProductImage(productId: UUID) -> Data? {
        let request = Product.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", productId as CVarArg)
        request.propertiesToFetch = ["images"]

        do {
            let results = try getContext().fetch(request)
            return results.first?.images
        } catch {
            print("Error fetching product image: \(error)")
            return nil
        }
    }
    // 通过类别获取产品
    func fetchByCategory(categoryId: UUID) -> [Product] {
        let predicate = NSPredicate(format: "category.id == %@", categoryId as CVarArg)
        return fetch(predicate: predicate)
    }

    // 获取即将过期的产品
    func fetchNearExpiry(daysThreshold: Int = 30) -> [Product] {
        let calendar = Calendar.current
        let startDate = Date()
        let endDate = calendar.date(byAdding: .day, value: daysThreshold, to: startDate)!

        let predicate = NSPredicate(format: "expiryDate >= %@ AND expiryDate <= %@", startDate as NSDate, endDate as NSDate)
        return fetch(predicate: predicate)
    }

    // 获取即将过保的产品
    func fetchNearWarrantyEnd(daysThreshold: Int = 30) -> [Product] {
        let calendar = Calendar.current
        let startDate = Date()
        let endDate = calendar.date(byAdding: .day, value: daysThreshold, to: startDate)!

        let predicate = NSPredicate(format: "warrantyEndDate >= %@ AND warrantyEndDate <= %@", startDate as NSDate, endDate as NSDate)
        return fetch(predicate: predicate)
    }

    // 搜索产品
    func search(query: String) -> [Product] {
        let predicate = NSPredicate(format: "name CONTAINS[cd] %@ OR brand CONTAINS[cd] %@ OR model CONTAINS[cd] %@", query, query, query)
        return fetch(predicate: predicate)
    }

    // 获取低使用率产品
    func fetchLowUsageProducts(daysThreshold: Int = 30) -> [Product] {
        // 获取所有产品
        let allProducts = fetchAll()

        // 筛选出低使用率产品
        let calendar = Calendar.current
        let now = Date()

        return allProducts.filter { product in
            if let lastUsageDate = product.lastUsageDate {
                // 如果有使用记录，检查最后使用日期
                let components = calendar.dateComponents([.day], from: lastUsageDate, to: now)
                return components.day ?? 0 >= daysThreshold
            } else if let purchaseDate = product.purchaseDate {
                // 如果没有使用记录，检查购买日期
                let components = calendar.dateComponents([.day], from: purchaseDate, to: now)
                return components.day ?? 0 >= daysThreshold
            }

            return false
        }
    }

    // 获取按状态分类的产品
    func fetchProductsByStatus() -> [Product.ProductStatus: [Product]] {
        let allProducts = fetchAll()
        var result = [Product.ProductStatus: [Product]]()

        for status in Product.ProductStatus.allCases {
            result[status] = allProducts.filter { $0.status == status }
        }

        return result
    }
}

// MARK: - 类别仓库
class CategoryRepository: CoreDataRepository<Category> {
    // 获取顶级类别
    func fetchRootCategories() -> [Category] {
        let predicate = NSPredicate(format: "parentCategory == nil")
        return fetch(predicate: predicate)
    }

    // 获取子类别
    func fetchChildCategories(of parentId: UUID) -> [Category] {
        let predicate = NSPredicate(format: "parentCategory.id == %@", parentId as CVarArg)
        return fetch(predicate: predicate)
    }
}

// MARK: - 使用记录仓库
class UsageRecordRepository: CoreDataRepository<UsageRecord> {
    // 获取特定产品的使用记录
    func fetchByProduct(productId: UUID) -> [UsageRecord] {
        let predicate = NSPredicate(format: "product.id == %@", productId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "date", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
}

// MARK: - 费用记录仓库
class RelatedExpenseRepository: CoreDataRepository<RelatedExpense> {
    // 获取特定产品的费用记录
    func fetchByProduct(productId: UUID) -> [RelatedExpense] {
        let predicate = NSPredicate(format: "product.id == %@", productId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "date", ascending: false)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
}

// MARK: - 费用类型仓库
class ExpenseTypeRepository: CoreDataRepository<ExpenseType> {
    // 通过名称获取费用类型
    func fetchByName(name: String) -> ExpenseType? {
        let predicate = NSPredicate(format: "name == %@", name)
        let results = fetch(predicate: predicate)
        return results.first
    }
}

// MARK: - 提醒仓库
class ReminderRepository: CoreDataRepository<Reminder> {
    // 获取特定产品的提醒
    func fetchByProduct(productId: UUID) -> [Reminder] {
        let predicate = NSPredicate(format: "product.id == %@", productId as CVarArg)
        let sortDescriptors = [NSSortDescriptor(key: "date", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }

    // 获取活跃的提醒
    func fetchActiveReminders() -> [Reminder] {
        let predicate = NSPredicate(format: "isActive == YES AND date >= %@", Date() as NSDate)
        let sortDescriptors = [NSSortDescriptor(key: "date", ascending: true)]
        return fetch(predicate: predicate, sortDescriptors: sortDescriptors)
    }
}
