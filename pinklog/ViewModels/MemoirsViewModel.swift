import Foundation
import CoreData
import SwiftUI
import Combine

class MemoirsViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var storyRecords: [UsageRecord] = []
    @Published var isDataLoaded: Bool = false
    @Published var filteredStoryRecords: [UsageRecord] = []
    @Published var searchText: String = ""
    @Published var isDescendingOrder: Bool = true // 默认降序（最新的故事在前面）
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // 分页相关
    private let pageSize = 20 // 每页加载20条记录
    private var currentPage = 0
    private var hasMoreData = true
    
    // MARK: - 私有属性
    private let context: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(context: NSManagedObjectContext) {
        self.context = context
        
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterStories()
            }
            .store(in: &cancellables)
        
        // 监听排序顺序变化
        $isDescendingOrder
            .sink { [weak self] _ in
                self?.sortStories()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 加载物品故事（分页加载）
    func loadAllStories() {
        // 重置分页状态
        currentPage = 0
        hasMoreData = true
        storyRecords.removeAll()

        loadNextPage()
    }

    /// 加载下一页故事
    func loadNextPage() {
        guard hasMoreData && !isLoading else { return }

        isLoading = true

        let request = NSFetchRequest<UsageRecord>(entityName: "UsageRecord")
        request.predicate = NSPredicate(format: "isStory == %@", NSNumber(value: true))
        request.sortDescriptors = [NSSortDescriptor(key: "date", ascending: !isDescendingOrder)]
        request.fetchLimit = pageSize
        request.fetchOffset = currentPage * pageSize

        do {
            let newRecords = try context.fetch(request)

            // 检查是否还有更多数据
            hasMoreData = newRecords.count == pageSize

            // 添加新记录到现有列表
            storyRecords.append(contentsOf: newRecords)
            currentPage += 1

            filterStories()
            isLoading = false
            isDataLoaded = true

            print("📖 加载第\(currentPage)页故事，本页\(newRecords.count)条，总计\(storyRecords.count)条")
            MemoryMonitor.shared.checkMemoryUsage()

        } catch {
            errorMessage = "加载故事失败: \(error.localizedDescription)"
            isLoading = false
        }
    }
    
    /// 切换排序顺序
    func toggleSortOrder() {
        isDescendingOrder.toggle()
    }
    
    /// 清除搜索
    func clearSearch() {
        searchText = ""
    }
    
    // MARK: - 私有方法
    
    /// 过滤故事
    private func filterStories() {
        if searchText.isEmpty {
            filteredStoryRecords = storyRecords
        } else {
            // 按标题或产品名称搜索
            filteredStoryRecords = storyRecords.filter { record in
                let titleMatch = record.title?.localizedCaseInsensitiveContains(searchText) ?? false
                let productNameMatch = record.product?.name?.localizedCaseInsensitiveContains(searchText) ?? false
                return titleMatch || productNameMatch
            }
        }
        
        // 应用排序
        sortStories()
    }
    
    /// 对过滤后的故事进行排序
    private func sortStories() {
        filteredStoryRecords.sort { first, second in
            guard let firstDate = first.date, let secondDate = second.date else {
                return false
            }
            
            return isDescendingOrder ? firstDate > secondDate : firstDate < secondDate
        }
    }
} 