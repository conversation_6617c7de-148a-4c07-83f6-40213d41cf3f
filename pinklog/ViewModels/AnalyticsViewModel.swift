import Foundation
import CoreData
import SwiftUI
import Combine

class AnalyticsViewModel: ObservableObject {
    // MARK: - 属性
    private let productRepository: ProductRepository
    private let categoryRepository: CategoryRepository
    private let expenseRepository: RelatedExpenseRepository

    @Published var products: [Product] = []
    @Published var isDataLoaded: Bool = false
    @Published var categories: [Category] = []
    @Published var reminders: [ReminderItem] = []
    @Published var activeReminders: [ReminderItem] = []
    @Published var categoryAnalysis: [CategoryAnalysis] = []
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false

    // 用户默认设置键
    private let readRemindersKey = "readReminders"

    // MARK: - 初始化
    init(productRepository: ProductRepository, categoryRepository: CategoryRepository) {
        self.productRepository = productRepository
        self.categoryRepository = categoryRepository

        // 获取 viewContext
        let viewContext = productRepository.getContext()

        self.expenseRepository = RelatedExpenseRepository(context: viewContext)

        print("📊 AnalyticsViewModel初始化，内存: \(String(format: "%.1f", MemoryMonitor.shared.getCurrentMemoryUsage()))MB")

        // 延迟加载，避免启动时内存峰值
        // loadData()
    }

    // MARK: - 数据加载
    func loadData() {
        isLoading = true

        // 加载产品
        products = productRepository.fetchAll()

        // 加载类别
        categories = categoryRepository.fetchAll()

        // 生成提醒
        generateReminders()

        // 生成类别分析
        generateCategoryAnalysis()

        isLoading = false
        isDataLoaded = true
    }

    // MARK: - 提醒生成
    private func generateReminders() {
        var newReminders: [ReminderItem] = []

        // 读取已读提醒ID
        let readReminderIds = UserDefaults.standard.stringArray(forKey: readRemindersKey) ?? []

        // 即将过期的产品
        let expiryProducts = productRepository.fetchNearExpiry()
        for product in expiryProducts {
            guard let expiryDate = product.expiryDate else { continue }

            let message = "将在\(formatRemainingDays(until: expiryDate))后过期"

            // 检查是否已读
            let reminderId = "过期-\(product.id?.uuidString ?? "")"
            let isRead = readReminderIds.contains(reminderId)

            let reminder = ReminderItem(
                product: product,
                type: .expiry,
                date: expiryDate,
                message: message,
                isRead: isRead
            )

            newReminders.append(reminder)
        }

        // 即将过保的产品
        let warrantyProducts = productRepository.fetchNearWarrantyEnd()
        for product in warrantyProducts {
            guard let warrantyEndDate = product.warrantyEndDate else { continue }

            let message = "保修将在\(formatRemainingDays(until: warrantyEndDate))后到期"

            // 检查是否已读
            let reminderId = "保修-\(product.id?.uuidString ?? "")"
            let isRead = readReminderIds.contains(reminderId)

            let reminder = ReminderItem(
                product: product,
                type: .warranty,
                date: warrantyEndDate,
                message: message,
                isRead: isRead
            )

            newReminders.append(reminder)
        }

        // 低使用率产品
        let lowUsageProducts = productRepository.fetchLowUsageProducts()
        for product in lowUsageProducts {
            let days = product.daysSinceLastUsage ?? 0
            let message = days > 0 ? "已有\(days)天未使用" : "购买后尚未使用"

            // 检查是否已读
            let reminderId = "低使用率-\(product.id?.uuidString ?? "")"
            let isRead = readReminderIds.contains(reminderId)

            let reminder = ReminderItem(
                product: product,
                type: .lowUsage,
                date: Date(),
                message: message,
                isRead: isRead
            )

            newReminders.append(reminder)
        }

        // 维护提醒
        generateMaintenanceReminders(newReminders: &newReminders, readReminderIds: readReminderIds)

        // 按日期排序
        reminders = newReminders.sorted { $0.date < $1.date }

        // 过滤出未读的提醒作为活跃提醒
        activeReminders = reminders.filter { !$0.isRead }
    }

    // 生成维护提醒
    private func generateMaintenanceReminders(newReminders: inout [ReminderItem], readReminderIds: [String]) {
        // 获取所有产品
        for product in products {
            // 检查是否需要维护提醒
            if shouldGenerateMaintenanceReminder(for: product) {
                let message = "建议进行定期维护，距离上次维护已经\(daysSinceLastMaintenance(for: product))天"

                // 检查是否已读
                let reminderId = "维护-\(product.id?.uuidString ?? "")"
                let isRead = readReminderIds.contains(reminderId)

                let reminder = ReminderItem(
                    product: product,
                    type: .maintenance,
                    date: Date(),
                    message: message,
                    isRead: isRead
                )

                newReminders.append(reminder)
            }
        }
    }

    // 判断产品是否需要维护提醒
    private func shouldGenerateMaintenanceReminder(for product: Product) -> Bool {
        // 获取产品的最后一次维护记录
        guard let productId = product.id else { return false }

        // 获取所有维护类型的费用记录
        let expenses = expenseRepository.fetchByProduct(productId: productId)
        let maintenanceExpenses = expenses.filter { expense in
            expense.type?.name == "保养" || expense.type?.name == "维修"
        }

        // 如果没有维护记录，且产品购买超过3个月，则建议维护
        if maintenanceExpenses.isEmpty {
            guard let purchaseDate = product.purchaseDate else { return false }
            let calendar = Calendar.current
            let now = Date()
            let components = calendar.dateComponents([.day], from: purchaseDate, to: now)
            return (components.day ?? 0) >= 90 // 3个月
        }

        // 获取最后一次维护日期
        let sortedExpenses = maintenanceExpenses.sorted { ($0.date ?? Date()) > ($1.date ?? Date()) }
        guard let lastMaintenance = sortedExpenses.first, let lastMaintenanceDate = lastMaintenance.date else { return false }

        // 计算距离上次维护的天数
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: lastMaintenanceDate, to: now)
        let days = components.day ?? 0

        // 根据产品类型和使用频率决定维护周期
        var maintenancePeriod = 180 // 默认6个月

        // 根据产品预期使用频率调整维护周期
        if let frequency = product.expectedUsageFrequency {
            switch frequency {
            case "每天":
                maintenancePeriod = 90 // 3个月
            case "每周数次":
                maintenancePeriod = 120 // 4个月
            case "每月数次":
                maintenancePeriod = 180 // 6个月
            case "偶尔使用":
                maintenancePeriod = 365 // 1年
            default:
                maintenancePeriod = 180
            }
        }

        return days >= maintenancePeriod
    }

    // 计算距离上次维护的天数
    private func daysSinceLastMaintenance(for product: Product) -> Int {
        guard let productId = product.id else { return 0 }

        // 获取所有维护类型的费用记录
        let expenses = expenseRepository.fetchByProduct(productId: productId)
        let maintenanceExpenses = expenses.filter { expense in
            expense.type?.name == "保养" || expense.type?.name == "维修"
        }

        // 如果没有维护记录，返回自购买日期的天数
        if maintenanceExpenses.isEmpty {
            guard let purchaseDate = product.purchaseDate else { return 0 }
            let calendar = Calendar.current
            let now = Date()
            let components = calendar.dateComponents([.day], from: purchaseDate, to: now)
            return components.day ?? 0
        }

        // 获取最后一次维护日期
        let sortedExpenses = maintenanceExpenses.sorted { ($0.date ?? Date()) > ($1.date ?? Date()) }
        guard let lastMaintenance = sortedExpenses.first, let lastMaintenanceDate = lastMaintenance.date else { return 0 }

        // 计算距离上次维护的天数
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: lastMaintenanceDate, to: now)
        return components.day ?? 0
    }

    // MARK: - 提醒管理

    // 标记提醒为已读
    func markReminderAsRead(_ reminder: ReminderItem) {
        guard let productId = reminder.product.id?.uuidString else { return }

        // 生成提醒ID
        let reminderId = "\(reminder.type.rawValue)-\(productId)"

        // 获取当前已读提醒列表
        var readReminderIds = UserDefaults.standard.stringArray(forKey: readRemindersKey) ?? []

        // 如果不在列表中，添加到列表
        if !readReminderIds.contains(reminderId) {
            readReminderIds.append(reminderId)
            UserDefaults.standard.set(readReminderIds, forKey: readRemindersKey)
        }

        // 更新提醒状态
        for i in 0..<reminders.count {
            if reminders[i].id == reminder.id {
                reminders[i].isRead = true
                break
            }
        }

        // 更新活跃提醒列表
        activeReminders = reminders.filter { !$0.isRead }
    }

    // 标记提醒为未读
    func markReminderAsUnread(_ reminder: ReminderItem) {
        guard let productId = reminder.product.id?.uuidString else { return }

        // 生成提醒ID
        let reminderId = "\(reminder.type.rawValue)-\(productId)"

        // 获取当前已读提醒列表
        var readReminderIds = UserDefaults.standard.stringArray(forKey: readRemindersKey) ?? []

        // 如果在列表中，从列表中移除
        if let index = readReminderIds.firstIndex(of: reminderId) {
            readReminderIds.remove(at: index)
            UserDefaults.standard.set(readReminderIds, forKey: readRemindersKey)
        }

        // 更新提醒状态
        for i in 0..<reminders.count {
            if reminders[i].id == reminder.id {
                reminders[i].isRead = false
                break
            }
        }

        // 更新活跃提醒列表
        activeReminders = reminders.filter { !$0.isRead }
    }

    // 切换提醒的已读/未读状态
    func toggleReminderReadStatus(_ reminder: ReminderItem) {
        if reminder.isRead {
            markReminderAsUnread(reminder)
        } else {
            markReminderAsRead(reminder)
        }
    }

    // 标记所有提醒为已读
    func markAllRemindersAsRead() {
        // 获取当前已读提醒列表
        var readReminderIds = UserDefaults.standard.stringArray(forKey: readRemindersKey) ?? []

        // 为所有提醒生成ID并添加到已读列表
        for reminder in reminders {
            guard let productId = reminder.product.id?.uuidString else { continue }
            let reminderId = "\(reminder.type.rawValue)-\(productId)"

            if !readReminderIds.contains(reminderId) {
                readReminderIds.append(reminderId)
            }
        }

        // 保存已读列表
        UserDefaults.standard.set(readReminderIds, forKey: readRemindersKey)

        // 更新所有提醒状态
        for i in 0..<reminders.count {
            reminders[i].isRead = true
        }

        // 清空活跃提醒列表
        activeReminders = []
    }

    // MARK: - 类别分析
    private func generateCategoryAnalysis() {
        var result: [CategoryAnalysis] = []

        for category in categories {
            guard let categoryId = category.id, let categoryName = category.name else { continue }

            let categoryProducts = productRepository.fetchByCategory(categoryId: categoryId)
            let productCount = categoryProducts.count

            if productCount == 0 { continue }

            let totalCost = categoryProducts.reduce(0) { $0 + $1.totalCostOfOwnership }
            let averageWorth = categoryProducts.reduce(0) { $0 + $1.worthItIndex() } / Double(productCount)

            let analysis = CategoryAnalysis(
                name: categoryName,
                productCount: productCount,
                totalCost: totalCost,
                averageWorth: averageWorth
            )

            result.append(analysis)
        }

        // 按总成本排序
        categoryAnalysis = result.sorted { $0.totalCost > $1.totalCost }
    }

    // MARK: - 辅助方法
    private func formatRemainingDays(until date: Date) -> String {
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: date)

        if let days = components.day {
            if days <= 0 {
                return "今天"
            } else if days == 1 {
                return "明天"
            } else if days < 30 {
                return "\(days)天"
            } else if days < 365 {
                let months = days / 30
                return "\(months)个月"
            } else {
                let years = days / 365
                return "\(years)年"
            }
        }

        return "未知时间"
    }

    // MARK: - 数据分析
    func getTotalProductsValue() -> Double {
        return products.reduce(0) { $0 + $1.price }
    }

    func getTotalProductsCount() -> Int {
        return products.count
    }

    func getTotalCostOfOwnership() -> Double {
        return products.reduce(0) { $0 + $1.totalCostOfOwnership }
    }

    func getAverageWorthIndex() -> Double {
        if products.isEmpty { return 0 }
        return products.reduce(0) { $0 + $1.worthItIndex() } / Double(products.count)
    }

    func getProductsByStatus() -> [Product.ProductStatus: [Product]] {
        var result = [Product.ProductStatus: [Product]]()

        for status in Product.ProductStatus.allCases {
            result[status] = products.filter { $0.status == status }
        }

        return result
    }

    func getProductsByCategory() -> [Category: [Product]] {
        var result = [Category: [Product]]()

        for product in products {
            if let category = product.category {
                if result[category] == nil {
                    result[category] = []
                }
                result[category]?.append(product)
            }
        }

        return result
    }

    func getProductsByPurchaseMotivation() -> [String: [Product]] {
        var result = [String: [Product]]()

        for product in products {
            let motivation = product.purchaseMotivation ?? "未知"
            if result[motivation] == nil {
                result[motivation] = []
            }
            result[motivation]?.append(product)
        }

        return result
    }

    func getMonthlyPurchaseAmount() -> [Date: Double] {
        var result = [Date: Double]()
        let calendar = Calendar.current

        for product in products {
            guard let purchaseDate = product.purchaseDate else { continue }

            // 获取年月
            let components = calendar.dateComponents([.year, .month], from: purchaseDate)
            if let year = components.year, let month = components.month,
               let monthDate = calendar.date(from: DateComponents(year: year, month: month)) {
                result[monthDate, default: 0] += product.price
            }
        }

        return result
    }
}

// MARK: - 提醒项模型
struct ReminderItem: Identifiable {
    let id: UUID
    let product: Product
    let type: ReminderType
    let date: Date
    let message: String
    var isRead: Bool

    init(product: Product, type: ReminderType, date: Date, message: String, isRead: Bool = false) {
        self.id = UUID()
        self.product = product
        self.type = type
        self.date = date
        self.message = message
        self.isRead = isRead
    }

    enum ReminderType: String {
        case expiry = "过期"
        case warranty = "保修"
        case lowUsage = "低使用率"
        case maintenance = "维护"
    }
}

// MARK: - 品类分析模型
struct CategoryAnalysis: Identifiable {
    let id = UUID()
    let name: String
    let productCount: Int
    let totalCost: Double
    let averageWorth: Double
}
