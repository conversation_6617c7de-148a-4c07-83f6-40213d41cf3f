import Foundation
import CoreData
import SwiftUI

class PurchaseChannelViewModel: ObservableObject {
    @Published var categories: [PurchaseChannelCategory] = []
    @Published var selectedCategory: PurchaseChannelCategory?
    @Published var channelsInSelectedCategory: [PurchaseChannel] = []
    
    private var viewContext: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.viewContext = context
        fetchCategories()
        
        // 如果没有类别，创建默认类别
        if categories.isEmpty {
            createDefaultCategories()
        }
    }
    
    // MARK: - 类别管理
    
    // 获取所有类别
    func fetchCategories() {
        let request = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
        request.sortDescriptors = [NSSortDescriptor(key: "name", ascending: true)]
        
        do {
            categories = try viewContext.fetch(request)
        } catch {
            print("获取购买渠道类别失败: \(error)")
        }
    }
    
    // 创建默认类别
    private func createDefaultCategories() {
        let onlineCategory = createCategory(name: "线上", icon: "network")
        let offlineCategory = createCategory(name: "线下", icon: "building.2")
        saveContext()
        
        // 为默认类别添加一些常用渠道
        if let online = onlineCategory {
            _ = addChannel(to: online, name: "淘宝")
            _ = addChannel(to: online, name: "京东")
            _ = addChannel(to: online, name: "拼多多")
        }
        
        if let offline = offlineCategory {
            _ = addChannel(to: offline, name: "实体店")
            _ = addChannel(to: offline, name: "超市")
        }
        
        saveContext()
        fetchCategories()
    }
    
    // 添加新类别
    func createCategory(name: String, icon: String? = nil) -> PurchaseChannelCategory? {
        let category = PurchaseChannelCategory(context: viewContext)
        category.id = UUID()
        category.name = name
        category.icon = icon
        
        do {
            try viewContext.save()
            fetchCategories()
            return category
        } catch {
            print("创建类别失败: \(error)")
            return nil
        }
    }
    
    // 更新类别
    func updateCategory(_ category: PurchaseChannelCategory, name: String, icon: String? = nil) {
        category.name = name
        if let icon = icon {
            category.icon = icon
        }
        
        saveContext()
        fetchCategories()
    }
    
    // 删除类别
    func deleteCategory(_ category: PurchaseChannelCategory) {
        viewContext.delete(category)
        saveContext()
        fetchCategories()
        
        if selectedCategory == category {
            selectedCategory = nil
            channelsInSelectedCategory = []
        }
    }
    
    // MARK: - 渠道管理
    
    // 获取指定类别下的所有渠道，按使用频率排序
    func fetchChannels(for category: PurchaseChannelCategory) {
        selectedCategory = category
        
        guard let channels = category.channels?.allObjects as? [PurchaseChannel] else {
            channelsInSelectedCategory = []
            return
        }
        
        // 按使用次数降序排序
        channelsInSelectedCategory = channels.sorted { $0.usageCount > $1.usageCount }
    }
    
    // 添加新渠道到指定类别
    func addChannel(to category: PurchaseChannelCategory, name: String, url: String? = nil, location: String? = nil) -> PurchaseChannel? {
        let channel = PurchaseChannel(context: viewContext)
        channel.id = UUID()
        channel.name = name
        channel.url = url
        channel.location = location
        channel.usageCount = 0
        channel.category = category
        
        do {
            try viewContext.save()
            if selectedCategory == category {
                fetchChannels(for: category)
            }
            return channel
        } catch {
            print("添加渠道失败: \(error)")
            return nil
        }
    }
    
    // 更新渠道
    func updateChannel(_ channel: PurchaseChannel, name: String, url: String? = nil, location: String? = nil) {
        channel.name = name
        if let url = url {
            channel.url = url
        }
        if let location = location {
            channel.location = location
        }
        
        saveContext()
        if let category = channel.category {
            fetchChannels(for: category)
        }
    }
    
    // 删除渠道
    func deleteChannel(_ channel: PurchaseChannel) {
        let categoryRef = channel.category
        viewContext.delete(channel)
        saveContext()
        
        if let category = categoryRef {
            fetchChannels(for: category)
        }
    }
    
    // 增加渠道使用次数
    func incrementChannelUsageCount(_ channel: PurchaseChannel) {
        channel.usageCount += 1
        saveContext()
        
        if let category = channel.category {
            fetchChannels(for: category)
        }
    }
    
    // 获取所有渠道（用于选择器）
    func getAllChannels() -> [PurchaseChannel] {
        let request = NSFetchRequest<PurchaseChannel>(entityName: "PurchaseChannel")
        request.sortDescriptors = [NSSortDescriptor(key: "usageCount", ascending: false)]
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("获取所有渠道失败: \(error)")
            return []
        }
    }
    
    // 获取最常用的渠道（前5个）
    func getMostUsedChannels() -> [PurchaseChannel] {
        let allChannels = getAllChannels()
        return Array(allChannels.prefix(5))
    }
    
    // 给定一个产品，更新它的购买渠道
    func updateProductPurchaseChannel(_ product: Product, channel: PurchaseChannel) {
        product.purchaseChannelRelation = channel
        incrementChannelUsageCount(channel)
        saveContext()
    }
    
    // 将旧的字符串购买渠道迁移到新的关系
    func migrateStringChannelToRelation(_ product: Product) {
        // 如果已经有关系了，就不需要迁移
        if product.purchaseChannelRelation != nil {
            return
        }
        
        // 如果有旧的字符串渠道
        if let oldChannelName = product.purchaseChannel, !oldChannelName.isEmpty {
            // 先查找是否有同名渠道
            let request = NSFetchRequest<PurchaseChannel>(entityName: "PurchaseChannel")
            request.predicate = NSPredicate(format: "name == %@", oldChannelName)
            
            do {
                let existingChannels = try viewContext.fetch(request)
                if let existingChannel = existingChannels.first {
                    // 使用现有渠道
                    product.purchaseChannelRelation = existingChannel
                    incrementChannelUsageCount(existingChannel)
                } else {
                    // 创建新渠道，默认放在"其他"类别
                    let otherCategory = getOrCreateOtherCategory()
                    if let newChannel = addChannel(to: otherCategory, name: oldChannelName) {
                        product.purchaseChannelRelation = newChannel
                    }
                }
                saveContext()
            } catch {
                print("迁移渠道失败: \(error)")
            }
        }
    }
    
    // 获取或创建"其他"类别
    private func getOrCreateOtherCategory() -> PurchaseChannelCategory {
        let request = NSFetchRequest<PurchaseChannelCategory>(entityName: "PurchaseChannelCategory")
        request.predicate = NSPredicate(format: "name == %@", "其他")
        
        do {
            let results = try viewContext.fetch(request)
            if let category = results.first {
                return category
            } else {
                if let category = createCategory(name: "其他", icon: "ellipsis.circle") {
                    return category
                }
            }
        } catch {
            print("获取或创建其他类别失败: \(error)")
        }
        
        // 如果出错，尝试返回第一个可用类别
        if let firstCategory = categories.first {
            return firstCategory
        }
        
        // 如果没有任何类别，创建一个新的默认类别
        return createCategory(name: "其他", icon: "ellipsis.circle")!
    }
    
    // 批量迁移所有产品的渠道字符串（分页处理以避免内存泄漏）
    func migrateAllProducts() {
        print("🔄 开始分页迁移产品渠道...")
        let pageSize = 50
        var offset = 0
        var hasMoreData = true
        var totalMigrated = 0

        while hasMoreData {
            autoreleasepool {
                let request = NSFetchRequest<Product>(entityName: "Product")
                request.fetchLimit = pageSize
                request.fetchOffset = offset
                request.propertiesToFetch = ["id", "purchaseChannel", "purchaseChannelRelation"]

                do {
                    let products = try viewContext.fetch(request)
                    hasMoreData = products.count == pageSize

                    for product in products {
                        migrateStringChannelToRelation(product)
                        totalMigrated += 1
                    }

                    // 每页处理完后保存并清理内存
                    try viewContext.save()
                    viewContext.refreshAllObjects()

                    offset += pageSize
                    print("📦 已迁移 \(totalMigrated) 个产品，当前页: \(products.count) 个")

                } catch {
                    print("❌ 分页迁移产品渠道失败: \(error)")
                    hasMoreData = false
                }
            }
        }

        print("✅ 产品渠道迁移完成，总计: \(totalMigrated) 个")
    }
    
    // 保存上下文
    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            print("保存上下文失败: \(error)")
        }
    }
} 