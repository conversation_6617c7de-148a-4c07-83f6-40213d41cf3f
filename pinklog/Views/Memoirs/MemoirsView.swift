import SwiftUI
import CoreData

struct MemoirsView: View {
    @StateObject private var viewModel: MemoirsViewModel
    @State private var isViewPreloaded = false
    @State private var scrollOffset: CGFloat = 0
    @State private var isSearchExpanded: Bool = false
    @State private var selectedViewMode: ViewMode = .cards
    @State private var animateHeader: Bool = false
    @State private var showQuote: Bool = true
    
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.colorScheme) private var colorScheme
    
    // 视图模式选择
    enum ViewMode {
        case cards // 卡片视图
        case timeline // 时间线视图
    }
    
    // 随机引导语
    private let welcomePhrases = [
        "欢迎回到你的回忆录",
        "这里珍藏着你的故事",
        "每个物品都是一段回忆",
        "重温那些美好瞬间",
        "你的故事，由此展开"
    ]
    @State private var welcomePhrase: String
    
    init(context: NSManagedObjectContext) {
        // 直接创建ViewModel，避免使用LazyLoadingManager
        _viewModel = StateObject(wrappedValue: MemoirsViewModel(context: context))
        // 随机选择一个欢迎语
        let randomIndex = Int.random(in: 0..<welcomePhrases.count)
        _welcomePhrase = State(initialValue: welcomePhrases[randomIndex])
    }
    
    var body: some View {
        ZStack {
            // 优雅背景
            MemoryBackground(theme: .pink)
            
            // 主要内容
            VStack(spacing: 0) {
                // 标题和搜索栏
                headerView
                
                // 内容区域
                if viewModel.isLoading {
                    // 加载中状态 - 优雅的加载动画
                    loadingView
                } else if viewModel.storyRecords.isEmpty {
                    // 空状态 - 精美的引导界面
                    emptyStateView
                } else if viewModel.filteredStoryRecords.isEmpty {
                    // 搜索无结果
                    noSearchResultsView
                } else {
                    // 故事列表/网格
                    ZStack {
                        // 内容视图
                        if selectedViewMode == .cards {
                            cardsGridView
                        } else {
                            timelineView
                        }
                        
                        // 引导语 - 只在顶部显示，滚动时隐藏
                        if showQuote {
                            VStack {
                                AnimatedQuote()
                                    .frame(height: 60)
                                    .padding(.horizontal)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.white.opacity(0.7))
                                            .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                                    )
                                    .padding(.horizontal)
                                    .padding(.top, 10)
                                
                                Spacer()
                            }
                            .transition(.opacity)
                        }
                    }
                }
            }
        }
        .navigationTitle("") // 不使用导航标题，使用自定义标题
        .navigationBarHidden(true)
        .onAppear {
            if !viewModel.isDataLoaded {
                viewModel.loadAllStories()
                
                // 触发标题动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    withAnimation(.easeOut(duration: 0.8)) {
                        animateHeader = true
                    }
                }
            } else {
                // 如果数据已加载，只触发标题动画
                withAnimation(.easeOut(duration: 0.5)) {
                    animateHeader = true
                }
            }
        }
    }
    
    // MARK: - 标题和搜索栏
    private var headerView: some View {
        VStack(spacing: 0) {
            // 顶部安全区域填充
            Color.clear
                .frame(height: 0)
                .background(BlurEffect(style: .systemThinMaterial))
            
            VStack(spacing: AppSizes.smallPadding) {
                // 标题区域
                HStack {
                    Text("物品回忆录")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(AppColors.text)
                        .opacity(animateHeader ? 1 : 0)
                        .offset(x: animateHeader ? 0 : -20)
                    
                    Spacer()
                    
                    // 视图模式切换
                    HStack(spacing: 12) {
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                selectedViewMode = .cards
                            }
                        }) {
                            Image(systemName: "square.grid.2x2")
                                .font(.system(size: 18, weight: selectedViewMode == .cards ? .semibold : .regular))
                                .foregroundColor(selectedViewMode == .cards ? AppColors.primary : AppColors.secondaryText)
                                .frame(width: 40, height: 36)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(selectedViewMode == .cards ? AppColors.primary.opacity(0.1) : Color.clear)
                                )
                        }
                        
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                selectedViewMode = .timeline
                            }
                        }) {
                            Image(systemName: "list.bullet")
                                .font(.system(size: 18, weight: selectedViewMode == .timeline ? .semibold : .regular))
                                .foregroundColor(selectedViewMode == .timeline ? AppColors.primary : AppColors.secondaryText)
                                .frame(width: 40, height: 36)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(selectedViewMode == .timeline ? AppColors.primary.opacity(0.1) : Color.clear)
                                )
                        }
                    }
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.white.opacity(0.6))
                            .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 2)
                    )
                    .opacity(animateHeader ? 1 : 0)
                    .offset(x: animateHeader ? 0 : 20)
                }
                
                // 搜索栏
                HStack {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(AppColors.secondaryText)
                            .padding(.leading, 8)
                        
                        TextField("搜索回忆...", text: $viewModel.searchText)
                            .font(.system(size: 16))
                            .padding(.vertical, 10)
                            .onTapGesture {
                                withAnimation(.spring()) {
                                    isSearchExpanded = true
                                    showQuote = false
                                }
                            }
                        
                        if !viewModel.searchText.isEmpty {
                            Button(action: {
                                viewModel.clearSearch()
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(AppColors.secondaryText)
                                    .padding(.trailing, 8)
                            }
                        }
                    }
                    .padding(.horizontal, 6)
                    .background(
                        BlurEffect(style: .systemMaterial)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.5), lineWidth: 0.5)
                            )
                    )
                    .frame(maxWidth: isSearchExpanded ? .infinity : 240)
                    
                    if isSearchExpanded {
                        Button("取消") {
                            withAnimation(.spring()) {
                                isSearchExpanded = false
                                viewModel.clearSearch()
                                hideKeyboard()
                                // 延迟显示引导语
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    withAnimation {
                                        showQuote = true
                                    }
                                }
                            }
                        }
                        .foregroundColor(AppColors.primary)
                        .padding(.leading, 10)
                        .transition(.move(edge: .trailing).combined(with: .opacity))
                    }
                    
                    if !isSearchExpanded {
                        // 排序控件
                        Button(action: {
                            viewModel.toggleSortOrder()
                            
                            // 触发小震动
                            let generator = UIImpactFeedbackGenerator(style: .light)
                            generator.impactOccurred()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: viewModel.isDescendingOrder ? "arrow.down" : "arrow.up")
                                    .font(.system(size: 12))
                                    .foregroundColor(AppColors.primary)
                                
                                Text(viewModel.isDescendingOrder ? "新" : "旧")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(AppColors.primary)
                            }
                            .padding(.vertical, 6)
                            .padding(.horizontal, 12)
                            .background(
                                Capsule()
                                    .fill(AppColors.primary.opacity(0.12))
                            )
                        }
                        .padding(.leading, 8)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .opacity(animateHeader ? 1 : 0)
                .offset(y: animateHeader ? 0 : -10)
            }
            .padding(.horizontal)
            .padding(.top, 12)
            .padding(.bottom, 10)
            .background(
                BlurEffect(style: .systemThinMaterial)
                    .opacity(0.95)
                    .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
    }
    
    // MARK: - 卡片网格视图
    private var cardsGridView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            // 使用LazyVGrid创建网格布局
            LazyVGrid(columns: [GridItem(.flexible())], spacing: 0) {
                // 使用LazyVStack包装每个卡片，提高性能
                ForEach(viewModel.filteredStoryRecords, id: \.self) { story in
                    // 使用延迟加载，每次渲染少量卡片
                    LazyView(
                        NavigationLink(destination: StoryDetailView(story: story)) {
                            StoryCardView(story: story)
                                .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        .accessibility(identifier: "storyCard\(story.id?.uuidString ?? "")")
                        .onAppear {
                            // 当接近列表底部时，加载下一页
                            if story == viewModel.filteredStoryRecords.last {
                                viewModel.loadNextPage()
                            }
                        }
                    )
                }
                
                // 底部填充
                Color.clear.frame(height: 30)
            }
            .padding(.top, 4)
            .onChange(of: viewModel.filteredStoryRecords) { _ in
                // 当内容变化时，显示引导语
                if !isSearchExpanded {
                    withAnimation {
                        showQuote = true
                    }
                }
            }
            // 添加视图出现和消失回调，优化内存管理
            .onAppear {
                // 将初在可视区域内的卡片标记为可见
                NotificationCenter.default.post(name: NSNotification.Name("StoryCardViewsAppeared"), object: nil)
            }
            .onDisappear {
                // 清理不在可视区域内的卡片缓存
                NotificationCenter.default.post(name: NSNotification.Name("StoryCardViewsDisappeared"), object: nil)
            }
            .simultaneousGesture(
                DragGesture()
                    .onChanged { value in
                        // 如果向下滚动超过20像素，显示引导语
                        if value.translation.height > 20 && !isSearchExpanded {
                            withAnimation {
                                showQuote = true
                            }
                        }
                        // 如果向上滚动超过20像素，隐藏引导语
                        else if value.translation.height < -20 {
                            withAnimation {
                                showQuote = false
                            }
                        }
                    }
            )
        }
        .refreshable {
            withAnimation(.easeInOut) {
                showQuote = true
            }
            viewModel.loadAllStories()
        }
    }
    
    // MARK: - 时间线视图
    private var timelineView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(spacing: 0) {
                ForEach(viewModel.filteredStoryRecords, id: \.self) { story in
                    NavigationLink(destination: StoryDetailView(story: story)) {
                        HStack(alignment: .top, spacing: 15) {
                            // 时间线指示器
                            VStack(spacing: 0) {
                                Circle()
                                    .fill(AppColors.primary)
                                    .frame(width: 12, height: 12)
                                
                                Rectangle()
                                    .fill(AppColors.primary.opacity(0.3))
                                    .frame(width: 2)
                                    .frame(maxHeight: .infinity)
                            }
                            .frame(width: 20)
                            .padding(.top, 4)
                            
                            // 内容区域
                            VStack(alignment: .leading, spacing: 8) {
                                // 日期
                                Text(story.getFormattedStoryDate(showRelative: false))
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(AppColors.secondaryText)
                                
                                // 标题和简介
                                VStack(alignment: .leading, spacing: 6) {
                                    if let title = story.title, !title.isEmpty {
                                        Text(title)
                                            .font(.system(size: 17, weight: .semibold))
                                            .foregroundColor(AppColors.text)
                                            .lineLimit(1)
                                    }
                                    
                                    Text(story.getStorySummary(maxLength: 100))
                                        .font(.system(size: 15))
                                        .foregroundColor(AppColors.secondaryText)
                                        .lineLimit(2)
                                        .multilineTextAlignment(.leading)
                                }
                                .padding()
                                .background(
                                    BlurEffect(style: .systemThinMaterial)
                                        .cornerRadius(12)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.white.opacity(0.5), lineWidth: 0.5)
                                        )
                                )
                                
                                // 底部信息
                                HStack {
                                    if let productName = story.product?.name {
                                        Label(productName, systemImage: "cube")
                                            .font(.system(size: 13))
                                            .foregroundColor(AppColors.secondaryText)
                                            .lineLimit(1)
                                    }
                                    
                                    Spacer()
                                    
                                    EmotionTag(emotionalValue: story.emotionalValue, compact: true)
                                }
                                .padding(.horizontal, 4)
                            }
                            .padding(.bottom, 20)
                        }
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
            .padding(.top, 20)
            .padding(.bottom, 30)
        }
        .refreshable {
            withAnimation(.easeInOut) {
                showQuote = true
            }
            viewModel.loadAllStories()
        }
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: AppSizes.padding) {
            Spacer()
            
            // 柔和的图像
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.pink.opacity(0.1), Color.purple.opacity(0.05)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 160, height: 160)
                
                Image(systemName: "book.closed")
                    .font(.system(size: 60))
                    .foregroundColor(AppColors.secondary.opacity(0.7))
            }
            .padding(.bottom, 10)
            
            Text("这里还没有故事")
                .font(.system(size: 24, weight: .semibold, design: .rounded))
                .foregroundColor(AppColors.text)
            
            Text("记录物品背后的珍贵回忆\n让它们不再只是简单的物品")
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 30)
                .padding(.bottom, 20)
            
            Button(action: {
                // 这里应该导航到添加故事的页面
                // 在实际实现中需要连接到AddUsageRecordView的故事模式
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 16))
                    
                    Text("记录第一个故事")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.vertical, 14)
                .padding(.horizontal, 26)
                .background(
                    LinearGradient(
                        colors: [AppColors.primary, AppColors.primary.opacity(0.8)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .clipShape(Capsule())
                .shadow(color: AppColors.primary.opacity(0.3), radius: 10, x: 0, y: 5)
            }
            
            Spacer()
            Spacer()
        }
    }
    
    // MARK: - 搜索无结果视图
    private var noSearchResultsView: some View {
        VStack(spacing: AppSizes.padding) {
            Spacer()
            
            Image(systemName: "magnifyingglass")
                .font(.system(size: 40, weight: .light))
                .foregroundColor(AppColors.secondaryText.opacity(0.6))
                .padding()
                .background(
                    Circle()
                        .fill(Color.white.opacity(0.7))
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                )
            
            Text("未找到相关回忆")
                .font(.system(size: 22, weight: .semibold, design: .rounded))
                .foregroundColor(AppColors.text)
                .padding(.top, 10)
            
            Text("尝试使用不同的关键词\n或清除搜索条件查看所有故事")
                .font(.system(size: 16))
                .foregroundColor(AppColors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 30)
                .padding(.bottom, 10)
            
            Button(action: {
                withAnimation {
                    viewModel.clearSearch()
                    hideKeyboard()
                }
            }) {
                Text("清除搜索")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColors.primary)
                    .padding(.vertical, 12)
                    .padding(.horizontal, 24)
                    .background(
                        Capsule()
                            .fill(AppColors.primary.opacity(0.1))
                    )
            }
            .padding(.top, 10)
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - 加载中视图
    private var loadingView: some View {
        VStack {
            Spacer()
            
            // 自定义加载动画
            ZStack {
                // 背景圆
                Circle()
                    .fill(Color.white.opacity(0.8))
                    .frame(width: 80, height: 80)
                    .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
                
                // 脉冲动画
                ForEach(0..<3) { index in
                    Circle()
                        .stroke(AppColors.primary.opacity(0.5), lineWidth: 2)
                        .frame(width: 60, height: 60)
                        .scaleEffect(1 + CGFloat(index) * 0.15)
                        .opacity(0.8 - CGFloat(index) * 0.3)
                        .animation(
                            Animation.easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                            value: 1 + CGFloat(index) * 0.15
                        )
                }
                
                // 中心图标
                Image(systemName: "book.pages")
                    .font(.system(size: 24))
                    .foregroundColor(AppColors.primary)
                    .rotationEffect(.degrees(animateHeader ? 15 : -15))
                    .animation(
                        Animation.easeInOut(duration: 2)
                            .repeatForever(autoreverses: true),
                        value: animateHeader
                    )
            }
            
            Text("正在加载回忆...")
                .font(.system(size: 18, weight: .medium, design: .rounded))
                .foregroundColor(AppColors.secondaryText)
                .padding(.top, 30)
            
            Spacer()
        }
    }
    
    // MARK: - 辅助函数
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - 预览
#Preview {
    NavigationStack {
        MemoirsView(context: PersistenceController.preview.container.viewContext)
    }
    .environmentObject(ThemeManager())
} 