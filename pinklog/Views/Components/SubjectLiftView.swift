//
//  SubjectLiftView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI

struct SubjectLiftView: View {
    @Binding var image: UIImage?
    @Binding var liftedImage: UIImage?
    
    @State private var isProcessing = false
    @State private var hasError = false
    @State private var errorMessage = ""
    @State private var showingOriginal = true
    @State private var isLongPressing = false
    
    var onImageProcessed: ((UIImage) -> Void)?
    
    var body: some View {
        VStack(spacing: 16) {
            if let currentImage = showingOriginal ? image : liftedImage {
                ZStack {
                    // 图片显示
                    Image(uiImage: currentImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(12)
                        .scaleEffect(isLongPressing ? 1.05 : 1.0)
                        .shadow(color: .black.opacity(isLongPressing ? 0.3 : 0.1), 
                               radius: isLongPressing ? 15 : 5, 
                               x: 0, y: isLongPressing ? 8 : 2)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isLongPressing)
                    
                    // 处理中的加载指示器
                    if isProcessing {
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.black.opacity(0.6))
                            
                            VStack(spacing: 12) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(1.2)
                                
                                Text("正在抠图...")
                                    .foregroundColor(.white)
                                    .font(.caption)
                            }
                        }
                    }
                    
                    // 长按提示
                    if !isProcessing && liftedImage == nil && showingOriginal {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                Text("长按抠出主体")
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.black.opacity(0.7))
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                                    .padding()
                            }
                        }
                    }
                }
                .onLongPressGesture(minimumDuration: 0.5, maximumDistance: 50) {
                    // 长按结束
                    withAnimation(.easeOut(duration: 0.2)) {
                        isLongPressing = false
                    }
                    
                    if let originalImage = image, liftedImage == nil {
                        startSubjectLift(originalImage)
                    }
                } onPressingChanged: { pressing in
                    // 长按状态变化
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isLongPressing = pressing
                    }
                    
                    if pressing {
                        // 触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                    }
                }
            }
            
            // 控制按钮
            if liftedImage != nil {
                HStack(spacing: 16) {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingOriginal = true
                        }
                    }) {
                        Text("原图")
                            .font(.caption)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(showingOriginal ? Color.blue : Color.gray.opacity(0.3))
                            .foregroundColor(showingOriginal ? .white : .primary)
                            .cornerRadius(8)
                    }
                    
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingOriginal = false
                        }
                    }) {
                        Text("抠图")
                            .font(.caption)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(!showingOriginal ? Color.blue : Color.gray.opacity(0.3))
                            .foregroundColor(!showingOriginal ? .white : .primary)
                            .cornerRadius(8)
                    }
                    
                    Button(action: {
                        if let processedImage = liftedImage {
                            onImageProcessed?(processedImage)
                        }
                    }) {
                        Text("使用抠图")
                            .font(.caption)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                }
            }
            
            // 错误信息
            if hasError {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .padding()
    }
    
    private func startSubjectLift(_ originalImage: UIImage) {
        isProcessing = true
        hasError = false
        
        SubjectLiftManager.shared.liftSubject(from: originalImage) { result in
            DispatchQueue.main.async {
                self.isProcessing = false
                
                switch result {
                case .success(let processedImage):
                    self.liftedImage = processedImage
                    self.showingOriginal = false
                    
                    // 成功反馈
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.success)
                    
                case .failure(let error):
                    self.hasError = true
                    self.errorMessage = error.localizedDescription
                    
                    // 错误反馈
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.error)
                }
            }
        }
    }
}

#Preview {
    SubjectLiftView(
        image: .constant(UIImage(systemName: "photo")),
        liftedImage: .constant(nil)
    )
}
