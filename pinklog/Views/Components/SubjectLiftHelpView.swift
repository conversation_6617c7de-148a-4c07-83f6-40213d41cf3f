//
//  SubjectLiftHelpView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI

struct SubjectLiftHelpView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text("智能抠图功能")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("使用 AI 技术自动识别并抠出物品主体")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.bottom, 10)
                    
                    // 使用步骤
                    VStack(alignment: .leading, spacing: 16) {
                        Text("使用步骤")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        stepView(
                            number: "1",
                            title: "拍照或选择图片",
                            description: "点击拍照按钮或从相册选择一张包含物品的图片",
                            icon: "camera.fill"
                        )
                        
                        stepView(
                            number: "2",
                            title: "长按开始抠图",
                            description: "在图片上长按0.5秒，系统会自动识别并抠出主体",
                            icon: "hand.tap.fill"
                        )
                        
                        stepView(
                            number: "3",
                            title: "选择使用结果",
                            description: "可以在原图和抠图结果之间切换，选择满意的版本",
                            icon: "checkmark.circle.fill"
                        )
                    }
                    
                    Divider()
                        .padding(.vertical, 10)
                    
                    // 使用技巧
                    VStack(alignment: .leading, spacing: 16) {
                        Text("使用技巧")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        tipView(
                            icon: "lightbulb.fill",
                            title: "拍照建议",
                            description: "确保物品与背景有明显对比，光线充足，物品完整在画面中"
                        )
                        
                        tipView(
                            icon: "eye.fill",
                            title: "最佳效果",
                            description: "单一物品、简洁背景的图片抠图效果最佳"
                        )
                        
                        tipView(
                            icon: "exclamationmark.triangle.fill",
                            title: "系统要求",
                            description: "该功能需要 iOS 16 或更高版本，较低版本会有功能限制"
                        )
                    }
                    
                    Divider()
                        .padding(.vertical, 10)
                    
                    // 常见问题
                    VStack(alignment: .leading, spacing: 16) {
                        Text("常见问题")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        faqView(
                            question: "抠图失败怎么办？",
                            answer: "可能是物品与背景对比不够明显，建议重新拍照或选择其他图片，也可以直接使用原图"
                        )
                        
                        faqView(
                            question: "抠图结果不满意？",
                            answer: "可以点击\"原图\"按钮切换回原始图片，或重新选择其他图片进行抠图"
                        )
                        
                        faqView(
                            question: "为什么没有抠图功能？",
                            answer: "该功能需要 iOS 16 或更高版本。如果您的设备不支持，可以直接使用原图"
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("使用帮助")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    private func stepView(number: String, title: String, description: String, icon: String) -> some View {
        HStack(alignment: .top, spacing: 16) {
            // 步骤编号
            ZStack {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 32, height: 32)
                
                Text(number)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: icon)
                        .foregroundColor(.blue)
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
    
    private func tipView(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.orange)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
    
    private func faqView(question: String, answer: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(question)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(answer)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    SubjectLiftHelpView()
}
