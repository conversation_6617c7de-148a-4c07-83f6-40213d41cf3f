import SwiftUI

struct ProductCard: View {
    let product: Product
    var showDetails: Bool = true

    @StateObject private var imageLoader = LazyImageLoader.shared
    @State private var productImage: UIImage?

    var body: some View {
        HStack(spacing: 12) {
                // 产品图片 - 使用按需加载避免内存泄漏
                ZStack {
                    if let image = productImage {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .cornerRadius(8)
                    } else {
                        Image(systemName: "photo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .padding(20)
                            .frame(width: 80, height: 80)
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }

                    // 状态标记 - 仅显示产品状态
                    VStack {
                        HStack {
                            Spacer()
                            Circle()
                                .fill(product.status.color)
                                .frame(width: 12, height: 12)
                        }

                        Spacer()
                    }
                    .padding(4)
                }
                .frame(width: 80, height: 80)

                // 产品信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未命名产品")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand + (product.model != nil ? " · \(product.model!)" : ""))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    if showDetails {
                        HStack {
                            Text("¥\(Int(product.price))")
                                .font(.subheadline)
                                .foregroundColor(.primary)

                            Spacer()

                            // 借阅状态标记（如果不是可借出状态）
                            if product.loanStatus != .available {
                                loanStatusBadge
                            }
                        }

                        Spacer()
                            .frame(height: 4)

                        // 值度和使用信息行
                        HStack {
                            // 使用信息
                            Text("使用\(product.totalUsageCount)次")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Spacer()

                            // 值度指数
                            HStack(spacing: 2) {
                                Text("值度")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text("\(Int(product.worthItIndex()))")
                                    .font(.subheadline)
                                    .foregroundColor(product.status.color)
                            }
                        }

                        // 只在有类别时显示类别行
                        if let category = product.category?.name {
                            HStack {
                                Spacer()
                                Text(category)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.2))
                                    .cornerRadius(4)
                            }
                        }
                    }
                }

                Spacer()
            }
            .padding(12)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
            .onAppear {
                // 按需加载图片
                if let productId = product.id {
                    let repository = ProductRepository(context: PersistenceController.shared.container.viewContext)
                    productImage = imageLoader.getProductImage(productId: productId, repository: repository)
                }
            }
    }

    // 借阅状态标记
    private var loanStatusBadge: some View {
        Text(product.loanStatus.rawValue)
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                Capsule()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [product.loanStatus.color, product.loanStatus.color.opacity(0.8)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: product.loanStatus.color.opacity(0.3), radius: 1, x: 0, y: 1)
            )
    }

    // 移除了不再需要的loadImage方法
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let product = Product(context: context)
    product.name = "MacBook Pro"
    product.brand = "Apple"
    product.model = "M1 Pro"
    product.price = 14999
    product.purchaseDate = Date()

    return ProductCard(product: product)
        .padding()
        .previewLayout(.sizeThatFits)
}
