//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var showingAddProduct = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var isProcessing = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                if showingAddProduct, let images = capturedImages {
                    // 显示添加产品表单
                    NavigationView {
                        AddProductView(
                            presetImage: images.original,
                            presetLiftedImage: images.lifted,
                            presetStickerImage: images.sticker
                        )
                        .navigationBarItems(leading: Button("取消") {
                            presentationMode.wrappedValue.dismiss()
                        })
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .offset(x: showingAddProduct ? 0 : geometry.size.width)
                } else {
                    // 显示拍照界面
                    CameraViewControllerWrapper { originalImage, liftedImg, stickerImg in
                        isProcessing = true

                        // 延迟一点时间让用户看到处理状态
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
                            onProcessCompleted?(originalImage, liftedImg, stickerImg)
                            isProcessing = false

                            // 优雅过渡到表单页面
                            withAnimation(.easeInOut(duration: 0.6)) {
                                showingAddProduct = true
                            }
                        }
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .offset(x: showingAddProduct ? -geometry.size.width : 0)
                }

                // 处理中的遮罩
                if isProcessing {
                    Color.black.opacity(0.7)
                        .ignoresSafeArea()

                    VStack(spacing: 20) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)

                        Text("正在处理图片...")
                            .foregroundColor(.white)
                            .font(.headline)
                    }
                }
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> CameraController {
        let controller = CameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }
    
    func updateUIViewController(_ uiViewController: CameraController, context: Context) {}
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
