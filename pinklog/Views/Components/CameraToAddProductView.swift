//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var showingAddProduct = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?

    var body: some View {
        ZStack {
            // 背景
            Color.black
                .ignoresSafeArea()

            if showingAddProduct, let images = capturedImages {
                // 添加产品表单 - 从底部滑入
                AddProductView(
                    presetImage: images.original,
                    presetLiftedImage: images.lifted,
                    presetStickerImage: images.sticker,
                    showNavigationBar: false
                )
                .background(Color(UIColor.systemBackground))
                .cornerRadius(showingAddProduct ? 0 : 20, corners: [.topLeft, .topRight])
                .offset(y: showingAddProduct ? 0 : UIScreen.main.bounds.height)
                .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showingAddProduct)

            } else {
                // 拍照界面
                CameraViewControllerWrapper { originalImage, liftedImg, stickerImg in
                    capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
                    onProcessCompleted?(originalImage, liftedImg, stickerImg)

                    // 立即开始优雅过渡
                    withAnimation(.spring(response: 0.35, dampingFraction: 0.8)) {
                        showingAddProduct = true
                    }
                }
                .opacity(showingAddProduct ? 0 : 1)
                .scaleEffect(showingAddProduct ? 0.8 : 1)
                .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showingAddProduct)
            }

            // 顶部导航栏
            VStack {
                HStack {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding(.leading)

                    Spacer()

                    if showingAddProduct {
                        Text("添加产品")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .transition(.opacity)
                    }

                    Spacer()

                    // 占位，保持居中
                    Color.clear
                        .frame(width: 44, height: 44)
                        .padding(.trailing)
                }
                .padding(.top, 10)

                Spacer()
            }
            .zIndex(1000)
        }
        .statusBarHidden(!showingAddProduct)
        .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showingAddProduct)
    }
}

// MARK: - 不自动关闭的相机控制器
class NonDismissingCameraController: CameraController {
    override func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        // 显示处理指示器
        showProcessingIndicator()

        SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
            DispatchQueue.main.async {
                self.hideProcessingIndicator()

                switch result {
                case .success(let (liftedImage, stickerImage)):
                    callback(originalImage, liftedImage, stickerImage)
                    // 不自动dismiss，让父视图控制
                case .failure(_):
                    // 处理失败，返回原图
                    callback(originalImage, originalImage, originalImage)
                    // 不自动dismiss，让父视图控制
                }
            }
        }
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> NonDismissingCameraController {
        let controller = NonDismissingCameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: NonDismissingCameraController, context: Context) {}
}

// MARK: - 扩展：特定角圆角
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
