//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var showingAddProduct = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                if showingAddProduct, let images = capturedImages {
                    // 显示添加产品表单
                    NavigationView {
                        AddProductView(
                            presetImage: images.original,
                            presetLiftedImage: images.lifted,
                            presetStickerImage: images.sticker
                        )
                        .navigationBarItems(leading: Button("取消") {
                            presentationMode.wrappedValue.dismiss()
                        })
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .offset(x: showingAddProduct ? 0 : geometry.size.width)
                } else {
                    // 显示拍照界面
                    CameraViewControllerWrapper { originalImage, liftedImg, stickerImg in
                        capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
                        onProcessCompleted?(originalImage, liftedImg, stickerImg)

                        // 延迟一点时间让处理动画完成，然后优雅过渡到表单页面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            withAnimation(.easeInOut(duration: 0.6)) {
                                showingAddProduct = true
                            }
                        }
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .offset(x: showingAddProduct ? -geometry.size.width : 0)
                }

            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - 不自动关闭的相机控制器
class NonDismissingCameraController: CameraController {
    override func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        // 显示处理指示器
        showProcessingIndicator()

        SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
            DispatchQueue.main.async {
                self.hideProcessingIndicator()

                switch result {
                case .success(let (liftedImage, stickerImage)):
                    callback(originalImage, liftedImage, stickerImage)
                    // 不自动dismiss，让父视图控制
                case .failure(_):
                    // 处理失败，返回原图
                    callback(originalImage, originalImage, originalImage)
                    // 不自动dismiss，让父视图控制
                }
            }
        }
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> NonDismissingCameraController {
        let controller = NonDismissingCameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: NonDismissingCameraController, context: Context) {}
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
