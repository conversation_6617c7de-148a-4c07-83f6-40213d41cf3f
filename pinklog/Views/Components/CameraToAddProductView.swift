//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var currentPhase: TransitionPhase = .camera
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var capturedImagePreview: UIImage?
    @State private var processingProgress: Double = 0.0

    enum TransitionPhase {
        case camera
        case processing
        case transitioning
        case form
    }

    var body: some View {
        ZStack {
            // 背景
            Color.black.ignoresSafeArea()

            switch currentPhase {
            case .camera:
                cameraView
                    .transition(.identity)

            case .processing:
                processingView
                    .transition(.opacity)

            case .transitioning:
                transitionView
                    .transition(.identity)

            case .form:
                formView
                    .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.1), value: currentPhase)
    }

    // MARK: - 相机视图
    private var cameraView: some View {
        CameraViewControllerWrapper { originalImage, liftedImg, stickerImg in
            capturedImagePreview = originalImage
            capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
            onProcessCompleted?(originalImage, liftedImg, stickerImg)

            // 开始处理阶段
            withAnimation(.easeInOut(duration: 0.3)) {
                currentPhase = .processing
            }

            // 模拟处理进度
            startProcessingAnimation()
        }
        .overlay(
            // 相机控制栏
            VStack {
                HStack {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .padding()

                    Spacer()
                }

                Spacer()
            }
        )
    }

    // MARK: - 处理视图
    private var processingView: some View {
        ZStack {
            // 背景模糊
            Color.black.opacity(0.9)

            VStack(spacing: 30) {
                // 预览图片
                if let preview = capturedImagePreview {
                    Image(uiImage: preview)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 200, height: 200)
                        .clipShape(RoundedRectangle(cornerRadius: 20))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.white.opacity(0.3), lineWidth: 2)
                        )
                        .scaleEffect(1.0 + sin(processingProgress * .pi * 2) * 0.02)
                }

                // 处理进度
                VStack(spacing: 16) {
                    Text("正在创建贴纸风格...")
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    // 自定义进度条
                    ZStack {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 200, height: 8)

                        RoundedRectangle(cornerRadius: 4)
                            .fill(
                                LinearGradient(
                                    colors: [.blue, .purple, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: 200 * processingProgress, height: 8)
                            .animation(.easeInOut(duration: 0.1), value: processingProgress)
                    }

                    Text("\(Int(processingProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
    }

    // MARK: - 过渡视图
    private var transitionView: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                colors: [.black, .gray.opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )

            VStack(spacing: 20) {
                // 成功图标
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
                    .scaleEffect(1.2)
                    .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentPhase)

                Text("处理完成")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text("正在跳转到编辑页面...")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
    }

    // MARK: - 表单视图
    private var formView: some View {
        Group {
            if let images = capturedImages {
                AddProductView(
                    presetImage: images.original,
                    presetLiftedImage: images.lifted,
                    presetStickerImage: images.sticker,
                    showCancelButton: false
                )
            } else {
                AddProductView(showCancelButton: false)
            }
        }
    }

    // MARK: - 处理动画
    private func startProcessingAnimation() {
        processingProgress = 0.0

        // 模拟处理进度
        Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { timer in
            processingProgress += 0.02

            if processingProgress >= 1.0 {
                timer.invalidate()

                // 显示完成状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    withAnimation(.easeInOut(duration: 0.4)) {
                        currentPhase = .transitioning
                    }

                    // 跳转到表单
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                            currentPhase = .form
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 不自动关闭的相机控制器
class NonDismissingCameraController: CameraController {
    override func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        // 显示处理指示器
        showProcessingIndicator()

        SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
            DispatchQueue.main.async {
                self.hideProcessingIndicator()

                switch result {
                case .success(let (liftedImage, stickerImage)):
                    callback(originalImage, liftedImage, stickerImage)
                    // 不自动dismiss，让父视图控制
                case .failure(_):
                    // 处理失败，返回原图
                    callback(originalImage, originalImage, originalImage)
                    // 不自动dismiss，让父视图控制
                }
            }
        }
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> NonDismissingCameraController {
        let controller = NonDismissingCameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: NonDismissingCameraController, context: Context) {}
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
