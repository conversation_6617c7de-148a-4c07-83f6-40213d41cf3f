import SwiftUI
import AVFoundation

struct ProductsView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var purchaseChannelViewModel: PurchaseChannelViewModel

    @State private var showingAddProduct = false
    @State private var showingFilterSheet = false
    @State private var showingCameraToAdd = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var searchText = ""

    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏
            searchBar

            // 筛选栏
            filterBar

            // 产品列表
            if productViewModel.isLoading {
                ProgressView()
                    .padding()
            } else if productViewModel.products.isEmpty {
                emptyStateView
            } else {
                productList
            }
        }
        .navigationTitle("我的产品")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    checkCameraPermissionForAdd()
                }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $showingAddProduct) {
            if let images = capturedImages {
                AddProductView(
                    presetImage: images.original,
                    presetLiftedImage: images.lifted,
                    presetStickerImage: images.sticker
                )
            } else {
                AddProductView()
            }
        }
        .fullScreenCover(isPresented: $showingCameraToAdd) {
            CameraToAddProductView { originalImage, liftedImg, stickerImg in
                capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
            }
        }
        .sheet(isPresented: $showingFilterSheet) {
            FilterView()
                .environmentObject(purchaseChannelViewModel)
        }
        .onChange(of: searchText) { newValue in
            productViewModel.searchText = newValue
            productViewModel.loadProducts()
        }
        .onAppear {
            if !productViewModel.isDataLoaded {
                productViewModel.loadProducts()
            }
            if !tagViewModel.isDataLoaded {
                tagViewModel.loadTags()
            }
        }
    }

    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索产品", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 8)
    }

    // 筛选栏
    private var filterBar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 排序按钮
                Menu {
                    ForEach(ProductViewModel.SortOption.allCases) { option in
                        Button(action: {
                            productViewModel.sortOption = option
                            productViewModel.loadProducts()
                        }) {
                            HStack {
                                Text(option.rawValue)
                                if productViewModel.sortOption == option {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text("排序")
                        Image(systemName: "arrow.up.arrow.down")
                    }
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(8)
                }

                // 筛选按钮
                ForEach(ProductViewModel.FilterOption.allCases) { option in
                    Button(action: {
                        // 清除高级筛选
                        if productViewModel.advancedFilterEnabled {
                            productViewModel.resetAdvancedFilters()
                        }
                        productViewModel.filterOption = option
                        productViewModel.loadProducts()
                    }) {
                        Text(option.rawValue)
                            .padding(.vertical, 6)
                            .padding(.horizontal, 12)
                            .background(productViewModel.filterOption == option && !productViewModel.advancedFilterEnabled ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground))
                            .foregroundColor(productViewModel.filterOption == option && !productViewModel.advancedFilterEnabled ? .white : .primary)
                            .cornerRadius(8)
                    }
                }

                // 更多筛选按钮
                Button(action: {
                    showingFilterSheet = true
                }) {
                    HStack {
                        Text(productViewModel.advancedFilterEnabled ? "已筛选 \(activeFilterCount)" : "更多")
                        Image(systemName: "slider.horizontal.3")
                    }
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(productViewModel.advancedFilterEnabled ? themeManager.currentTheme.primaryColor : Color(UIColor.tertiarySystemBackground))
                    .foregroundColor(productViewModel.advancedFilterEnabled ? .white : .primary)
                    .cornerRadius(8)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
    }

    // 计算激活的筛选条件数量
    private var activeFilterCount: Int {
        var count = 0

        if !productViewModel.advancedFilterCategories.isEmpty { count += 1 }
        if !productViewModel.advancedFilterStatuses.isEmpty { count += 1 }
        if !productViewModel.advancedFilterTags.isEmpty { count += 1 }
        if !productViewModel.advancedFilterChannels.isEmpty { count += 1 }
        if productViewModel.dateFilterEnabled { count += 1 }
        if productViewModel.priceFilterEnabled { count += 1 }
        if productViewModel.satisfactionFilterEnabled { count += 1 }
        if productViewModel.worthFilterEnabled { count += 1 }
        if productViewModel.warrantyFilterEnabled { count += 1 }
        if productViewModel.usageFilterEnabled { count += 1 }

        return count
    }

    // 产品列表
    private var productList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(productViewModel.products) { product in
                    NavigationLink(destination: ProductDetailView(product: product)
                        .environmentObject(themeManager)
                        .environmentObject(usageViewModel)
                        .environmentObject(productViewModel)) {
                        ProductCard(product: product)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "cube.box")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            Text("暂无产品")
                .font(.title2)
                .foregroundColor(.primary)

            Text("点击右上角的"+"按钮拍照添加您的第一个产品")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            VStack(spacing: 12) {
                Button(action: {
                    checkCameraPermissionForAdd()
                }) {
                    Text("拍照添加产品")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.vertical, 12)
                        .padding(.horizontal, 24)
                        .background(themeManager.currentTheme.primaryColor)
                        .cornerRadius(10)
                }

                Button(action: {
                    capturedImages = nil
                    showingAddProduct = true
                }) {
                    Text("手动添加产品")
                        .font(.subheadline)
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 16)
                        .background(Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(themeManager.currentTheme.primaryColor, lineWidth: 1)
                        )
                }
            }
            .padding(.top, 10)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }

    // MARK: - 相机权限检查
    private func checkCameraPermissionForAdd() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            showingCameraToAdd = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.showingCameraToAdd = true
                    }
                }
            }
        case .denied, .restricted:
            // 可以显示权限提示
            break
        @unknown default:
            break
        }
    }
}

#Preview {
    NavigationView {
        let context = PersistenceController.preview.container.viewContext
        ProductsView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: context),
                categoryRepository: CategoryRepository(context: context)
            ))
            .environmentObject(TagViewModel(
                repository: TagRepository(context: context)
            ))
            .environmentObject(ThemeManager())
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: context),
                expenseRepository: RelatedExpenseRepository(context: context)
            ))
            .environmentObject(PurchaseChannelViewModel(context: context))
    }
}
