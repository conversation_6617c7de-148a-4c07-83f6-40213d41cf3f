import SwiftUI
import PhotosUI
import Combine
import AVFoundation

struct AddProductView: View {
    // 预设图片参数
    let presetImage: UIImage?
    let presetLiftedImage: UIImage?
    let presetStickerImage: UIImage?

    // 快速录入模式切换
    @State private var isQuickEntryMode: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var purchaseChannelViewModel: PurchaseChannelViewModel

    let showCancelButton: Bool
    let onSaveCompleted: (() -> Void)?

    // 初始化方法
    init(presetImage: UIImage? = nil, presetLiftedImage: UIImage? = nil, presetStickerImage: UIImage? = nil, showCancelButton: Bool = true, onSaveCompleted: (() -> Void)? = nil) {
        self.presetImage = presetImage
        self.presetLiftedImage = presetLiftedImage
        self.presetStickerImage = presetStickerImage
        self.showCancelButton = showCancelButton
        self.onSaveCompleted = onSaveCompleted
    }

    // 分步表单控制
    @State var currentStep: Int = 1
    @State var totalSteps: Int = 4 // u4e3au4e86u517cu5bb9u6027u4fdd u7559u8be5u5c5eu6027uff0cu5b9eu9645u4f7fu7528actualTotalSteps
    
    // u6839u636eu5f53u524du6a21u5f0fu8ba1u7b97u5b9eu9645u7684u6b65u9aa4u6570
    private var actualTotalSteps: Int {
        if isQuickMode {
            // 如果有预设图片（来自拍照），直接一步完成
            return hasPresetImage ? 1 : 2
        } else {
            return 4 // 详细模式四步
        }
    }

    // 是否有预设图片
    private var hasPresetImage: Bool {
        return presetImage != nil
    }
    
    // 录入模式选择
    @State var isQuickMode: Bool = true // 默认使用快速录入模式

    // 基本信息
    @State var name: String = ""
    @State var brand: String = ""
    @State private var model: String = ""
    @State var price: String = ""
    @State var purchaseDate: Date = Date()
    @State var selectedCategory: Category?
    @State private var purchaseChannel: String = ""
    @State private var quantity: Int = 1
    @State private var valuationMethod: String = "usage" // 默认为按使用次数计算
    @State private var isVirtualProduct: Bool = false // 新增：是否为虚拟商品

    // 图片
    @State var selectedImage: UIImage?
    @State var liftedImage: UIImage?
    @State var stickerImage: UIImage?
    @State var isShowingImagePicker = false
    @State var isShowingCamera = false
    @State var isShowingImageOptions = false
    @State var isShowingSubjectLiftHelp = false
    @State private var photoPickerItems: [PhotosPickerItem] = []

    // 关键日期
    @State private var expiryDate: Date?
    @State private var showExpiryDate = false

    // 扩展信息
    @State private var purchaseMotivation: String = ""
    @State private var initialSatisfaction: Int = 3
    @State private var purchaseNotes: String = ""
    @State private var expectedLifespan: String = ""
    @State private var expectedUsageFrequency: String = ""

    // 标签
    @State private var showingTagSelector = false

    // 其他
    @State var showingCategorySelector = false
    @State var showingAlert = false
    @State var alertMessage = ""
    @State private var isFormValid: Bool = false
    @State private var isSaving: Bool = false

    // 动机选项
    private let motivations = ["必需品", "提升效率", "兴趣爱好", "冲动消费", "礼物", "替代旧物", "其他"]

    // 计算方式选项
    private let valuationMethods = [
        ("usage", "按使用次数"),
        ("daily", "按使用天数")
    ]

    @State private var selectedPurchaseChannel: PurchaseChannel?
    @State private var showingPurchaseChannelPicker = false

    // 快速模式步骤标题
    private var quickModeStepTitles: [String] {
        return hasPresetImage ? ["快速录入"] : ["快速录入", "预览与保存"]
    }
    
    // 详细模式步骤标题
    private let detailedModeStepTitles = [
        "基本信息",
        "图片与计算方式",
        "日期与保修",
        "标签与扩展信息"
    ]
    
    // 获取当前模式的步骤标题
    private var stepTitles: [String] {
        return isQuickMode ? quickModeStepTitles : detailedModeStepTitles
    }
    
    // 模式切换视图
    private var modeToggleView: some View {
        VStack(spacing: 8) {
            HStack {
                Text("录入模式")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 12) {
                // 快速录入按钮
                modeButton(title: "快速录入", icon: "bolt.fill", description: "仅填写核心字段", isSelected: isQuickMode) {
                    if currentStep > 1 {
                        // 如果不是第一步，需要提示用户
                        alertMessage = "切换录入模式会重置当前表单，确定继续吗？"
                        showingAlert = true
                    } else if !isQuickMode {
                        // 只有当当前不是快速模式时才切换
                        withAnimation {
                            isQuickMode = true
                            currentStep = 1
                            validateCurrentStep()
                        }
                    }
                }
                
                // 详细录入按钮
                modeButton(title: "详细录入", icon: "list.bullet.clipboard", description: "填写所有字段", isSelected: !isQuickMode) {
                    if currentStep > 1 {
                        // 如果不是第一步，需要提示用户
                        alertMessage = "切换录入模式会重置当前表单，确定继续吗？"
                        showingAlert = true
                    } else if isQuickMode {
                        // 只有当当前是快速模式时才切换
                        withAnimation {
                            isQuickMode = false
                            currentStep = 1
                            validateCurrentStep()
                        }
                    }
                }
            }
        }
        .padding(.horizontal)
        .padding(.top, 16)
        .padding(.bottom, 8)
    }
    
    // 日期格式化工具
    // u6a21u5f0fu9009u62e9u6309u94ae
    private func modeButton(title: String, icon: String, description: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: icon)
                        .font(.headline)
                    Text(title)
                        .font(.headline)
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(12)
            .background(isSelected ? themeManager.currentTheme.primaryColor.opacity(0.15) : Color.gray.opacity(0.1))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? themeManager.currentTheme.primaryColor : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var purchaseDateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
    
    // 信息行
    private func infoRow(title: String, value: String, icon: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.gray)
                .frame(width: 24)
            
            Text(title)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontWeight(.medium)
        }
    }
    
    // 快速录入表单
    private var quickEntrySectionView: some View {
        Section {
            // 名称输入
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("产品名称")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                
                TextField("请输入产品名称", text: $name)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(name.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
            }
            .padding(.vertical, 4)
            
            // 图片选择器（简化版）
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("产品图片")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()

                    Button(action: {
                        isShowingSubjectLiftHelp = true
                    }) {
                        Image(systemName: "questionmark.circle")
                            .foregroundColor(.blue)
                            .font(.caption)
                    }
                }

                if let image = selectedImage {
                    // 显示已选择的图片和抠图功能
                    HStack {
                        Spacer()
                        SubjectLiftView(
                            image: $selectedImage,
                            liftedImage: $liftedImage,
                            stickerImage: $stickerImage
                        ) { processedImage in
                            selectedImage = processedImage
                        }
                        Spacer()
                    }

                    // 更换图片按钮
                    Button(action: {
                        isShowingImageOptions = true
                    }) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("更换图片")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    // 选择图片按钮
                    Button(action: {
                        isShowingImageOptions = true
                    }) {
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(height: 180)
                                .cornerRadius(12)

                            VStack(spacing: 12) {
                                Image(systemName: "camera.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.gray)

                                Text("点击拍照或选择图片")
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.vertical, 4)
            
            // 价格输入
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("价格")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                
                TextField("请输入价格", text: $price)
                    .keyboardType(.decimalPad)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(price.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
            }
            .padding(.vertical, 4)
            
            // 日期选择
            VStack(alignment: .leading, spacing: 8) {
                Text("购买日期")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                DatePicker("", selection: $purchaseDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)
            
            // 类别选择
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("类别")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                
                Button(action: {
                    showingCategorySelector = true
                }) {
                    HStack {
                        Text(selectedCategory?.name ?? "请选择类别")
                            .foregroundColor(selectedCategory == nil ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(selectedCategory == nil ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
                }
            }
            .padding(.vertical, 4)
            
            // 必填项提示
            HStack {
                Text("*")
                    .foregroundColor(.red)
                Text("表示必填项")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 8)
            
            VStack(alignment: .leading, spacing: 8) {
                Text(hasPresetImage ?
                     "提示: 图片已自动添加，填写必填信息后即可直接保存" :
                     "提示: 快速录入仅包含核心信息，保存后可以在详情页补充更多信息")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background((hasPresetImage ? Color.green : Color.blue).opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)
        }
    }
    
    // 快速预览表单
    private var quickPreviewSectionView: some View {
        Section {
            // 标题
            VStack(alignment: .leading) {
                Text("预览信息")
                    .font(.headline)
                    .padding(.bottom, 12)
                
                // 分割线
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.gray.opacity(0.2))
                    .padding(.bottom, 16)
            }
            .padding(.vertical, 8)
            
            // 快速预览内容
            Group {
                // 图片
                if let image = selectedImage {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFit()
                        .frame(height: 180)
                        .frame(maxWidth: .infinity)
                        .cornerRadius(12)
                        .padding(.bottom, 16)
                }
                
                // 信息汇总
                VStack(spacing: 16) {
                    // 标题和价格
                    HStack(alignment: .top) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(name)
                                .font(.title3)
                                .fontWeight(.bold)
                            
                            if !brand.isEmpty {
                                Text(brand)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Text("¥\(price)")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }
                    
                    // 其他信息
                    VStack(spacing: 12) {
                        infoRow(title: "类别", value: selectedCategory?.name ?? "未选择", icon: "folder")
                        infoRow(title: "购买日期", value: purchaseDateFormatter.string(from: purchaseDate), icon: "calendar")
                    }
                    
                    // 分割线
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color.gray.opacity(0.2))
                        .padding(.vertical, 8)
                    
                    // 提示信息
                    HStack(spacing: 16) {
                        Image(systemName: "info.circle")
                            .foregroundColor(.orange)
                        
                        Text("保存后可在产品详情页补充更多信息，如保修、标签、使用记录等")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 录入模式已使用按钮形式，此处留空
                    EmptyView()
                        .padding(.top, 16)
                    // 模式切换开关
                    modeToggleView
                    
                    // 进度指示器
                    progressIndicator

                    // 步骤标题
                    HStack {
                        Text(stepTitles[min(currentStep - 1, stepTitles.count - 1)])
                            .font(.headline)
                            .padding(.horizontal)
                            .padding(.top, 8)
                        Spacer()
                    }

                    // 表单内容 - 改为VStack而不是Form
                    VStack {
                        Group {
                            if isQuickMode {
                                // 快速模式表单
                                switch currentStep {
                                case 1:
                                    quickEntrySectionView
                                case 2:
                                    quickPreviewSectionView
                                default:
                                    EmptyView()
                                }
                            } else {
                                // 详细模式表单
                                switch currentStep {
                                case 1:
                                    basicInfoSection
                                case 2:
                                    Group {
                                        imageSection
                                        valuationMethodSection
                                    }
                                case 3:
                                    Group {
                                        keyDatesSection
                                        warrantyInfoSection
                                    }
                                case 4:
                                    Group {
                                        tagsSection
                                        extendedInfoSection
                                    }
                                default:
                                    EmptyView()
                                }
                            }
                        }
                        .padding(.horizontal)

                        // 导航按钮
                        navigationButtonsSection
                            .padding(.horizontal)
                            .padding(.bottom, 20)
                    }
                    .padding(.top, 10)
                }
            }
            .navigationTitle("添加产品")
            .navigationBarItems(
                leading: showCancelButton ? Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                } : nil
            )
            .sheet(isPresented: $showingCategorySelector) {
                CategorySelectorView(selectedCategory: $selectedCategory)
            }
            .sheet(isPresented: $showingTagSelector) {
                TagSelectorView()
            }
            .sheet(isPresented: $showingPurchaseChannelPicker) {
                PurchaseChannelPickerView(viewModel: purchaseChannelViewModel, selectedChannel: $selectedPurchaseChannel)
                    .environmentObject(themeManager)
            }
            .photosPicker(isPresented: $isShowingImagePicker, selection: $photoPickerItems, maxSelectionCount: 1, matching: .images)
            .onChange(of: photoPickerItems) { _, newItems in
                guard let item = newItems.first else { return }
                Task {
                    if let data = try? await item.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        DispatchQueue.main.async {
                            self.selectedImage = image
                            self.liftedImage = nil // 重置抠图结果
                            self.stickerImage = nil // 重置贴纸结果
                        }
                    }
                }
            }
            .sheet(isPresented: $isShowingCamera) {
                AutoProcessCameraView { originalImage, liftedImg, stickerImg in
                    selectedImage = originalImage
                    liftedImage = liftedImg
                    stickerImage = stickerImg
                }
            }
            .actionSheet(isPresented: $isShowingImageOptions) {
                ActionSheet(
                    title: Text("选择图片"),
                    message: Text("请选择获取图片的方式"),
                    buttons: [
                        .default(Text("拍照")) {
                            checkCameraPermission()
                        },
                        .default(Text("从相册选择")) {
                            isShowingImagePicker = true
                        },
                        .cancel(Text("取消"))
                    ]
                )
            }
            .sheet(isPresented: $isShowingSubjectLiftHelp) {
                SubjectLiftHelpView()
            }
            .fileImporter(
                isPresented: $productViewModel.showWarrantyFileImporter,
                allowedContentTypes: productViewModel.allowedWarrantyContentTypes,
                allowsMultipleSelection: false
            ) { result_in_array in
                let singleResult: Result<URL, Error>
                switch result_in_array {
                case .success(let urls):
                    if let firstUrl = urls.first {
                        singleResult = .success(firstUrl)
                    } else {
                        singleResult = .failure(NSError(domain: "AddProductView", code: 0, userInfo: [NSLocalizedDescriptionKey: "No file URL found after selection."]))
                    }
                case .failure(let error):
                    singleResult = .failure(error)
                }
                productViewModel.handleWarrantyFileSelection(result: singleResult)
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("提示"), 
                    message: Text(alertMessage), 
                    primaryButton: .default(Text("确定")) {
                        if alertMessage.contains("切换录入模式") {
                            withAnimation {
                                isQuickMode.toggle()
                                currentStep = 1
                                validateCurrentStep()
                            }
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
            .onAppear {
                productViewModel.resetWarrantyInputs()
                tagViewModel.clearSelection()

                // 设置预设图片
                if let preset = presetImage {
                    selectedImage = preset
                }
                if let presetLifted = presetLiftedImage {
                    liftedImage = presetLifted
                }
                if let presetSticker = presetStickerImage {
                    stickerImage = presetSticker
                }

                // 初始化时设置表单为有效状态，只在保存时验证
                isFormValid = true
            }
            // 移除实时验证，只在保存时验证
        }
    }

    // 进度指示器
    private var progressIndicator: some View {
        VStack(spacing: 4) {
            // 进度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景条
                    Rectangle()
                        .foregroundColor(Color.gray.opacity(0.2))
                        .frame(height: 8)
                        .cornerRadius(4)

                    // 进度条
                    Rectangle()
                        .foregroundColor(themeManager.currentTheme.primaryColor)
                        .frame(width: geometry.size.width * CGFloat(currentStep) / CGFloat(actualTotalSteps), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
            .padding(.horizontal)

            // 步骤指示器
            HStack {
                ForEach(1...actualTotalSteps, id: \.self) { step in
                    Spacer()
                    VStack(spacing: 4) {
                        ZStack {
                            Circle()
                                .fill(step <= currentStep ? themeManager.currentTheme.primaryColor : Color.gray.opacity(0.3))
                                .frame(width: 24, height: 24)

                            Text("\(step)")
                                .font(.caption)
                                .foregroundColor(.white)
                        }

                        Text(stepTitles[step - 1])
                            .font(.caption)
                            .foregroundColor(step == currentStep ? themeManager.currentTheme.primaryColor : .gray)
                    }
                    Spacer()
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 8)
        }
        .padding(.top, 16)
    }

    // 导航按钮
    private var navigationButtonsSection: some View {
        Section {
            if currentStep == actualTotalSteps && hasPresetImage {
                // 拍照模式：保存按钮居中显示
                VStack {
                    Button(action: validateAndSaveProduct) {
                        HStack {
                            if isSaving {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("保存中...")
                            } else {
                                Text("保存")
                                Image(systemName: "checkmark")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .foregroundColor(.white)
                        .padding(.vertical, 12)
                        .background(isSaving ? Color.gray : themeManager.currentTheme.primaryColor)
                        .cornerRadius(10)
                    }
                    .disabled(isSaving)
                    .buttonStyle(BorderlessButtonStyle())
                }
                .padding(.horizontal)
            } else {
                // 普通模式：左右布局
                HStack {
                    // 上一步按钮
                    if currentStep > 1 {
                        Button(action: {
                            withAnimation {
                                currentStep -= 1
                                validateCurrentStep()
                            }
                        }) {
                            HStack {
                                Image(systemName: "chevron.left")
                                Text("上一步")
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .padding(.vertical, 8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    } else {
                        Spacer()
                    }

                    Spacer()
                        .frame(width: 20)

                    // 下一步/保存按钮
                    if currentStep < actualTotalSteps {
                        Button(action: {
                            if validateCurrentStep() {
                                withAnimation {
                                    currentStep += 1
                                    validateCurrentStep()
                                }
                            }
                        }) {
                            HStack {
                                Text("下一步")
                                Image(systemName: "chevron.right")
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background(isFormValid ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!isFormValid)
                        .buttonStyle(BorderlessButtonStyle())
                    } else {
                        Button(action: validateAndSaveProduct) {
                            HStack {
                                if isSaving {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                    Text("保存中...")
                                } else {
                                    Text("保存")
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding(.vertical, 8)
                            .background((isFormValid && !isSaving) ? themeManager.currentTheme.primaryColor : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!isFormValid || isSaving)
                        .buttonStyle(BorderlessButtonStyle())
                    }
                }
            }
        }
    }

    // MARK: - Form Sections

    private var basicInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("产品名称")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                TextField("请输入产品名称", text: $name)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(name.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("品牌")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("请输入品牌（选填）", text: $brand)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("型号/款式")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("请输入型号或款式（选填）", text: $model)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("价格")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                TextField("请输入价格", text: $price)
                    .keyboardType(.decimalPad)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(price.isEmpty ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("购买日期")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                DatePicker("", selection: $purchaseDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
                    .labelsHidden()
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("类别")
                    Text("*")
                        .foregroundColor(.red)
                }
                .font(.subheadline)
                .foregroundColor(.secondary)

                Button(action: {
                    showingCategorySelector = true
                }) {
                    HStack {
                        Text(selectedCategory?.name ?? "请选择类别")
                            .foregroundColor(selectedCategory == nil ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(selectedCategory == nil ? Color.red.opacity(0.5) : Color.clear, lineWidth: 1)
                    )
                }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("购买渠道")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Button(action: {
                    showingPurchaseChannelPicker = true
                }) {
                    HStack {
                        Text(selectedPurchaseChannel?.name ?? "请选择购买渠道（选填）")
                            .foregroundColor(selectedPurchaseChannel == nil ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text("数量")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Stepper("数量: \(quantity)", value: $quantity, in: 1...99)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .padding(.vertical, 4)

            VStack(alignment: .leading, spacing: 4) {
                Toggle("是否为虚拟商品（如数字订阅）", isOn: $isVirtualProduct)
                    .padding(.vertical, 4)
            }
            .padding(.vertical, 4)


            // 必填项提示
            HStack {
                Text("*")
                    .foregroundColor(.red)
                Text("表示必填项")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 8)
        }
    }

    private var valuationMethodSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("价值计算方式")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                CustomSegmentedControl(
                    selection: $valuationMethod,
                    items: valuationMethods.map { $0.0 }
                ) { methodKey in
                    Text(valuationMethods.first { $0.0 == methodKey }?.1 ?? "")
                }
                .accentColor(themeManager.currentTheme.primaryColor)

                if valuationMethod == "daily" {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        Text("像冰箱、热水器等每天持续服役的产品，建议选择按天计算")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                } else {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        Text("像衣物、工具等间歇使用的产品，建议选择按使用次数计算")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)
        }
    }

    private var imageSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("产品图片")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()

                    Button(action: {
                        isShowingSubjectLiftHelp = true
                    }) {
                        Image(systemName: "questionmark.circle")
                            .foregroundColor(.blue)
                            .font(.caption)
                    }
                }

                if let image = selectedImage {
                    // 显示已选择的图片和抠图功能
                    SubjectLiftView(
                        image: $selectedImage,
                        liftedImage: $liftedImage,
                        stickerImage: $stickerImage
                    ) { processedImage in
                        selectedImage = processedImage
                    }

                    // 更换图片按钮
                    Button(action: {
                        isShowingImageOptions = true
                    }) {
                        HStack {
                            Image(systemName: "arrow.triangle.2.circlepath")
                            Text("更换图片")
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    // 选择图片按钮
                    Button(action: {
                        isShowingImageOptions = true
                    }) {
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(height: 200)
                                .cornerRadius(12)

                            VStack(spacing: 12) {
                                Image(systemName: "camera.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.gray)

                                Text("点击拍照或选择图片")
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.vertical, 4)
        }
    }

    private var keyDatesSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("有效期设置")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Toggle("设置产品有效期", isOn: $showExpiryDate)
                    .padding(.vertical, 4)
                    .tint(themeManager.currentTheme.primaryColor)

                if showExpiryDate {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("有效期至")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        DatePicker("", selection: Binding(
                            get: { expiryDate ?? Date().addingTimeInterval(86400 * 30) },
                            set: { expiryDate = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.orange)
                        Text("设置有效期后，系统将在产品即将过期时提醒您")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: showExpiryDate)
        }
    }

    private var warrantyInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("保修信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Toggle("提供保修信息", isOn: $productViewModel.hasWarranty)
                    .padding(.vertical, 4)
                    .tint(themeManager.currentTheme.primaryColor)

                if productViewModel.hasWarranty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修截止日期")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        DatePicker("", selection: Binding(
                            get: { productViewModel.warrantyEndDateInput ?? Date().addingTimeInterval(86400 * 365) },
                            set: { productViewModel.warrantyEndDateInput = $0 }
                        ), displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修范围摘要")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        TextEditor(text: $productViewModel.warrantyDetailsInput)
                            .frame(minHeight: 80)
                            .padding(4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修凭证")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Button(action: {
                            productViewModel.showWarrantyFileImporter = true
                        }) {
                            HStack {
                                if let fileName = productViewModel.selectedWarrantyFileName, !fileName.isEmpty {
                                    Image(systemName: "doc.text.fill")
                                        .foregroundColor(themeManager.currentTheme.primaryColor)
                                    Text(fileName)
                                        .lineLimit(1)
                                        .truncationMode(.middle)
                                } else {
                                    Image(systemName: "icloud.and.arrow.up")
                                        .foregroundColor(.secondary)
                                    Text("上传保修凭证（选填）")
                                        .foregroundColor(.secondary)
                                }
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())

                        if productViewModel.selectedWarrantyFileURL != nil || (productViewModel.currentWarrantyImagePath != nil && !productViewModel.currentWarrantyImagePath!.isEmpty) {
                            Button(action: {
                                productViewModel.clearSelectedWarrantyFile()
                            }) {
                                HStack {
                                    Spacer()
                                    Text("清除已选凭证")
                                        .font(.caption)
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.caption)
                                }
                                .foregroundColor(.red)
                                .padding(.top, 4)
                            }
                            .buttonStyle(BorderlessButtonStyle())
                            .transition(.opacity)
                        }
                    }
                    .padding(.vertical, 4)
                    .transition(.opacity)
                }
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: productViewModel.hasWarranty)
        }
    }

    private var tagsSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("标签")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Button(action: {
                    showingTagSelector = true
                }) {
                    HStack {
                        if tagViewModel.selectedTags.isEmpty {
                            Text("选择标签（选填）")
                                .foregroundColor(.secondary)
                        } else {
                            Text("已选择 \(tagViewModel.selectedTags.count) 个标签")
                                .foregroundColor(.primary)
                        }
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())

                if !tagViewModel.selectedTags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(Array(tagViewModel.selectedTags), id: \.self) { tag in
                                HStack(spacing: 4) {
                                    Circle()
                                        .fill(tagViewModel.getTagColor(tag))
                                        .frame(width: 8, height: 8)

                                    Text(tag.name ?? "")
                                        .font(.caption)
                                }
                                .padding(.horizontal, 10)
                                .padding(.vertical, 6)
                                .background(tagViewModel.getTagColor(tag).opacity(0.1))
                                .foregroundColor(tagViewModel.getTagColor(tag))
                                .cornerRadius(20)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    .transition(.opacity)
                }

                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                    Text("标签可以帮助您更好地组织和筛选产品")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
            .padding(.vertical, 4)
            .animation(.easeInOut, value: tagViewModel.selectedTags.count)
        }
    }

    private var extendedInfoSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                Text("扩展信息")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                VStack(alignment: .leading, spacing: 4) {
                    Text("购买动机")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Picker("", selection: $purchaseMotivation) {
                        Text("请选择").tag("")
                        ForEach(motivations, id: \.self) { motivation in
                            Text(motivation).tag(motivation)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("初始满意度")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= initialSatisfaction ? "star.fill" : "star")
                                .foregroundColor(index <= initialSatisfaction ? .yellow : .gray)
                                .font(.title2)
                                .onTapGesture {
                                    withAnimation {
                                        initialSatisfaction = index
                                    }
                                }
                        }

                        Spacer()

                        Text(satisfactionText(for: initialSatisfaction))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("预期使用寿命")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextField("例如: 3年, 1000小时（选填）", text: $expectedLifespan)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("预期使用频率")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextField("例如: 每天, 每周3次（选填）", text: $expectedUsageFrequency)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)

                VStack(alignment: .leading, spacing: 4) {
                    Text("备注")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    TextEditor(text: $purchaseNotes)
                        .frame(minHeight: 100)
                        .padding(4)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.vertical, 4)
            }
            .padding(.vertical, 4)
        }
    }

    // 根据满意度返回文字描述
    private func satisfactionText(for rating: Int) -> String {
        switch rating {
        case 1:
            return "非常不满意"
        case 2:
            return "不太满意"
        case 3:
            return "一般"
        case 4:
            return "比较满意"
        case 5:
            return "非常满意"
        default:
            return "未评价"
        }
    }

    // MARK: - Helper Methods

    // 验证当前步骤（仅用于步骤切换，不再实时验证）
    func validateCurrentStep() -> Bool {
        if isQuickMode {
            // 快速模式验证
            switch currentStep {
            case 1:
                // 验证基本信息
                if name.isEmpty || price.isEmpty || selectedCategory == nil {
                    isFormValid = false
                    return false
                }

                // 验证价格格式
                guard let priceValue = Double(price), priceValue > 0 else {
                    isFormValid = false
                    return false
                }

                isFormValid = true
                return true

            case 2:
                // 预览页面，不需要额外验证
                isFormValid = true
                return true

            default:
                isFormValid = false
                return false
            }
        } else {
            // 详细模式验证
            switch currentStep {
            case 1:
                // 验证基本信息
                if name.isEmpty || price.isEmpty || selectedCategory == nil {
                    isFormValid = false
                    return false
                }

                // 验证价格格式
                guard let priceValue = Double(price), priceValue > 0 else {
                    isFormValid = false
                    return false
                }

                isFormValid = true
                return true

            case 2:
                // 图片和计算方式步骤，没有必填项
                isFormValid = true
                return true

            case 3:
                // 日期和保修信息步骤，没有必填项
                isFormValid = true
                return true

            case 4:
                // 标签和扩展信息步骤，没有必填项
                isFormValid = true
                return true

            default:
                isFormValid = false
                return false
            }
        }
    }

    // 验证并保存产品
    private func validateAndSaveProduct() {
        // 防止重复保存
        guard !isSaving else {
            print("⚠️ 正在保存中，忽略重复请求")
            return
        }

        print("🚀 开始保存产品...")
        isSaving = true

        // 最终验证
        guard !name.isEmpty else {
            print("❌ 产品名称为空")
            alertMessage = "请输入产品名称"
            showingAlert = true
            isSaving = false
            return
        }

        guard let priceValue = Double(price), priceValue > 0 else {
            print("❌ 价格无效: '\(price)'")
            alertMessage = "请输入有效的价格"
            showingAlert = true
            isSaving = false
            return
        }

        guard selectedCategory != nil else {
            print("❌ 未选择类别")
            alertMessage = "请选择产品类别"
            showingAlert = true
            isSaving = false
            return
        }

        print("✅ 最终验证通过，开始保存...")
        print("- 产品名称: \(name)")
        print("- 价格: \(priceValue)")
        print("- 类别: \(selectedCategory?.name ?? "nil")")
        print("- 图片: \(selectedImage != nil ? "有" : "无")")

        // 保存产品
        saveProduct()
    }

    // 检查相机权限
    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            isShowingCamera = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.isShowingCamera = true
                    } else {
                        self.alertMessage = "需要相机权限才能拍照，请在设置中允许访问相机"
                        self.showingAlert = true
                    }
                }
            }
        case .denied, .restricted:
            alertMessage = "需要相机权限才能拍照，请在设置中允许访问相机"
            showingAlert = true
        @unknown default:
            alertMessage = "相机权限状态未知"
            showingAlert = true
        }
    }

    // 保存产品
    private func saveProduct() {
        print("💾 开始保存产品到数据库...")
        withAnimation {
            let newProduct = Product(context: viewContext)

            // 基本信息
            newProduct.id = UUID()
            newProduct.name = name
            newProduct.brand = brand
            newProduct.model = model
            newProduct.price = Double(price) ?? 0
            newProduct.purchaseDate = purchaseDate
            newProduct.category = selectedCategory

            // 购买渠道处理
            if let channel = selectedPurchaseChannel {
                // 使用新的关系模型
                newProduct.purchaseChannelRelation = channel
                purchaseChannelViewModel.incrementChannelUsageCount(channel)
                // 同时保留字符串值以确保兼容性
                newProduct.purchaseChannel = channel.name
            } else if !purchaseChannel.isEmpty {
                // 如果直接输入了渠道名称但没有选择渠道对象，尝试查找或创建对应的渠道
                newProduct.purchaseChannel = purchaseChannel
                purchaseChannelViewModel.migrateStringChannelToRelation(newProduct)
            }

            newProduct.quantity = Int16(quantity)
            newProduct.valuationMethod = valuationMethod
            newProduct.isVirtualProduct = isVirtualProduct // 保存虚拟商品状态

            // 图片 - 使用用户最终选择的图片（selectedImage已经是用户选择的结果）
            if let image = selectedImage {
                let imageData = ImageManager.shared.processTransparentImage(image) ?? image.jpegData(compressionQuality: 0.8)
                newProduct.images = imageData
            }

            // 关键日期
            if showExpiryDate, let expiryDate = expiryDate {
                newProduct.expiryDate = expiryDate
            }

            if productViewModel.hasWarranty, let warrantyEndDate = productViewModel.warrantyEndDateInput {
                newProduct.warrantyEndDate = warrantyEndDate
                newProduct.warrantyDetails = productViewModel.warrantyDetailsInput
                newProduct.warrantyImage = productViewModel.selectedWarrantyFileName

                // Handle warranty file copying if needed
                if let sourceURL = productViewModel.selectedWarrantyFileURL {
                    productViewModel.saveWarrantyFile(from: sourceURL, for: newProduct)
                }
            }

            // 扩展信息
            newProduct.purchaseMotivation = purchaseMotivation
            newProduct.initialSatisfaction = Int16(initialSatisfaction)
            newProduct.purchaseNotes = purchaseNotes
            newProduct.expectedLifespan = expectedLifespan.isEmpty ? 0 : Int16(expectedLifespan) ?? 0
            newProduct.expectedUsageFrequency = expectedUsageFrequency

            // 标签
            for tag in tagViewModel.selectedTags {
                newProduct.addToTags(tag)
            }

            // 保存
            do {
                print("💾 正在保存到Core Data...")
                try viewContext.save()
                print("✅ 保存成功！")

                // 平滑将新产品添加到列表
                DispatchQueue.main.async {
                    print("📱 更新UI并关闭页面...")
                    // 使用平滑添加方法避免刷新闪烁
                    productViewModel.smoothlyAddProduct(newProduct)

                    // 重置保存状态
                    self.isSaving = false

                    // 如果有自定义关闭回调，使用它；否则使用默认的dismiss
                    if let onSaveCompleted = self.onSaveCompleted {
                        print("🔄 使用自定义关闭回调")
                        onSaveCompleted()
                    } else {
                        print("🔄 使用默认dismiss")
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            } catch {
                let nsError = error as NSError
                print("❌ 保存失败: \(nsError), \(nsError.userInfo)")
                alertMessage = "保存失败：\(nsError.localizedDescription)"
                showingAlert = true
                isSaving = false
            }
        }
    }
}

struct AddProductView_Previews: PreviewProvider {
    static var previews: some View {
        AddProductView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(TagViewModel(
                repository: TagRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
    }
}