import SwiftUI
import AVFoundation

struct DashboardView: View {
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var analyticsViewModel: AnalyticsViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var usageViewModel: UsageViewModel

    @State private var showingAddProduct = false
    @State private var showingCamera = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var showingCameraToAdd = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 顶部统计卡片
                statsSection

                // 提醒区域
                remindersSection

                // 产品状态分布
                productStatusSection

                // 最近添加的产品
                recentProductsSection
            }
            .padding()
        }
        .navigationTitle("仪表盘")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    checkCameraPermissionForAdd()
                }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $showingAddProduct) {
            if let images = capturedImages {
                AddProductView(
                    presetImage: images.original,
                    presetLiftedImage: images.lifted,
                    presetStickerImage: images.sticker
                )
            } else {
                AddProductView()
            }
        }
        .fullScreenCover(isPresented: $showingCameraToAdd) {
            CameraToAddProductView { originalImage, liftedImg, stickerImg in
                capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
            }
        }
        .onAppear {
            if !productViewModel.isDataLoaded {
                productViewModel.loadProducts()
            }
            if !analyticsViewModel.isDataLoaded {
                analyticsViewModel.loadData()
            }
        }
        .refreshable {
            productViewModel.loadProducts()
            analyticsViewModel.loadData()
        }
    }

    // 统计卡片区域
    private var statsSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                // 总产品数
                StatCard(
                    title: "总产品数",
                    value: "\(productViewModel.getTotalProductsCount())",
                    icon: "cube.box.fill",
                    color: themeManager.currentTheme.primaryColor
                )

                // 总投资
                StatCard(
                    title: "总投资",
                    value: "¥\(Int(productViewModel.getTotalProductsValue()))",
                    icon: "yensign.circle.fill",
                    color: .green
                )
            }

            HStack(spacing: 16) {
                // 总持有成本
                StatCard(
                    title: "总持有成本",
                    value: "¥\(Int(analyticsViewModel.getTotalCostOfOwnership()))",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .orange
                )

                // 平均值度
                StatCard(
                    title: "平均值度",
                    value: "\(Int(analyticsViewModel.getAverageWorthIndex()))",
                    icon: "star.fill",
                    color: .yellow
                )
            }
        }
    }

    // 提醒区域
    @ViewBuilder
    private var remindersSection: some View {
        if !analyticsViewModel.activeReminders.isEmpty {
            VStack(alignment: .leading, spacing: 12) {
                Text("提醒")
                    .font(.headline)
                    .padding(.horizontal)

                ForEach(analyticsViewModel.activeReminders.prefix(3)) { reminder in
                    NavigationLink(destination: ProductDetailView(product: reminder.product)
                        .environmentObject(themeManager)
                        .environmentObject(usageViewModel)
                        .environmentObject(productViewModel)) {
                        HStack(spacing: 12) {
                            // 图标
                            Image(systemName: reminderIcon(for: reminder.type))
                                .font(.title2)
                                .foregroundColor(reminderColor(for: reminder.type))
                                .frame(width: 40, height: 40)
                                .background(reminderColor(for: reminder.type).opacity(0.2))
                                .cornerRadius(8)

                            VStack(alignment: .leading, spacing: 4) {
                                Text(reminder.product.name ?? "未命名产品")
                                    .font(.headline)

                                Text(reminder.message)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()
                        }
                        .padding()
                        .background(Color(UIColor.secondarySystemBackground))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                if analyticsViewModel.activeReminders.count > 3 {
                    NavigationLink(destination: RemindersView()
                        .environmentObject(analyticsViewModel)
                        .environmentObject(themeManager)
                        .environmentObject(productViewModel)
                        .environmentObject(usageViewModel)) {
                        Text("查看全部\(analyticsViewModel.activeReminders.count)条提醒")
                            .font(.subheadline)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                            .padding(.horizontal)
                    }
                }
            }
        }
    }

    // 产品状态分布
    private var productStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("产品状态分布")
                .font(.headline)
                .padding(.horizontal)

            let statusProducts = productViewModel.getProductsByStatus()

            HStack(spacing: 16) {
                ForEach(Product.ProductStatus.allCases) { status in
                    let count = statusProducts[status]?.count ?? 0

                    VStack(spacing: 8) {
                        Text("\(count)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(status.color)

                        Text(status.rawValue)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 最近添加的产品
    private var recentProductsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近添加")
                .font(.headline)
                .padding(.horizontal)

            ForEach(productViewModel.products.prefix(3)) { product in
                NavigationLink(destination: ProductDetailView(product: product)
                    .environmentObject(themeManager)
                    .environmentObject(usageViewModel)
                    .environmentObject(productViewModel)) {
                    ProductCard(product: product)
                }
            }

            if !productViewModel.products.isEmpty {
                HStack {
                    Button(action: {
                        // 查看全部产品
                    }) {
                        Text("查看全部\(productViewModel.products.count)个产品")
                            .font(.subheadline)
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                    }

                    Spacer()

                    Button(action: {
                        capturedImages = nil
                        showingAddProduct = true
                    }) {
                        Text("手动添加")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    // 提醒图标
    private func reminderIcon(for type: ReminderItem.ReminderType) -> String {
        switch type {
        case .expiry:
            return "calendar.badge.exclamationmark"
        case .warranty:
            return "shield.lefthalf.fill"
        case .lowUsage:
            return "hourglass.bottomhalf.fill"
        case .maintenance:
            return "wrench.and.screwdriver"
        }
    }

    // 提醒颜色
    private func reminderColor(for type: ReminderItem.ReminderType) -> Color {
        switch type {
        case .expiry:
            return .red
        case .warranty:
            return .orange
        case .lowUsage:
            return .blue
        case .maintenance:
            return .green
        }
    }

    // MARK: - 相机权限检查
    private func checkCameraPermissionForAdd() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            showingCameraToAdd = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.showingCameraToAdd = true
                    }
                }
            }
        case .denied, .restricted:
            // 可以显示权限提示
            break
        @unknown default:
            break
        }
    }
}

#Preview {
    NavigationView {
        DashboardView()
            .environmentObject(ProductViewModel(
                repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(AnalyticsViewModel(
                productRepository: ProductRepository(context: PersistenceController.preview.container.viewContext),
                categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
            ))
            .environmentObject(ThemeManager())
            .environmentObject(UsageViewModel(
                usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
                expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
            ))
    }
}
