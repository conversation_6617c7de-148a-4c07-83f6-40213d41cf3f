import SwiftUI

// 导入回忆录相关组件
import CoreData

struct MainView: View {
    init() {
        // 禁用预加载以避免内存泄漏
        // DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        //     LazyLoadingManager.shared.preloadMemoirsViewModel(context: PersistenceController.shared.container.viewContext)
        // }
        print("📱 MainView初始化，内存使用: \(String(format: "%.1f", MemoryMonitor.shared.getCurrentMemoryUsage()))MB")
    }
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var analyticsViewModel: AnalyticsViewModel
    @EnvironmentObject var tagViewModel: TagViewModel
    @EnvironmentObject var purchaseAnalyticsViewModel: PurchaseAnalyticsViewModel
    @EnvironmentObject var simulationViewModel: SimulationViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    // 移除了 loanRecordViewModel
    @EnvironmentObject var themeManager: ThemeManager

    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab.animation(.easeInOut(duration: 0.2))) {
            // 仪表盘
            NavigationStack {
                DashboardView()
                    .environmentObject(productViewModel)
                    .environmentObject(analyticsViewModel)
                    .environmentObject(themeManager)
                    .environmentObject(usageViewModel)
            }
            .opacity(selectedTab == 0 ? 1 : 0)
            .transition(.opacity)
            .tabItem {
                Label("仪表盘", systemImage: "chart.bar.doc.horizontal")
            }
            .tag(0)

            // 产品
            NavigationStack {
                ProductsView()
                    .environmentObject(productViewModel)
                    .environmentObject(tagViewModel)
                    .environmentObject(themeManager)
                    .environmentObject(usageViewModel)
            }
            .opacity(selectedTab == 1 ? 1 : 0)
            .transition(.opacity)
            .tabItem {
                Label("产品", systemImage: "cube.box")
            }
            .tag(1)
            
            // 回忆录
            NavigationStack {
                ZStack {
                    if selectedTab == 2 || abs(selectedTab - 2) < 2 {
                        MemoirsView(context: viewContext)
                            .environmentObject(themeManager)
                    } else {
                        Color.clear // 占位符
                    }
                }
            }
            .opacity(selectedTab == 2 ? 1 : 0)
            .transition(.opacity)
            .tabItem {
                Label("回忆录", systemImage: "book.closed")
            }
            .tag(2)

            // 分析
            NavigationStack {
                AnalyticsView()
                    .environmentObject(analyticsViewModel)
                    .environmentObject(purchaseAnalyticsViewModel)
                    .environmentObject(simulationViewModel)
                    .environmentObject(themeManager)
            }
            .opacity(selectedTab == 3 ? 1 : 0)
            .transition(.opacity)
            .tabItem {
                Label("分析", systemImage: "chart.pie")
            }
            .tag(3)

            // 我的
            NavigationStack {
                ProfileView()
                    .environmentObject(themeManager)
                    .environmentObject(analyticsViewModel)
                    .environmentObject(productViewModel)
                    .environmentObject(usageViewModel)
            }
            .opacity(selectedTab == 4 ? 1 : 0)
            .transition(.opacity)
            .tabItem {
                Label("我的", systemImage: "person.circle")
            }
            .tag(4)
        }
        .accentColor(themeManager.currentTheme.primaryColor)
        .transition(.opacity)
        .animation(.easeInOut(duration: 0.3), value: selectedTab)
        .onAppear {
            // 设置全局Tab Bar外观
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()

            UITabBar.appearance().standardAppearance = appearance
            if #available(iOS 15.0, *) {
                UITabBar.appearance().scrollEdgeAppearance = appearance
            }

            // 确保加载所有标签
            tagViewModel.loadTags()
        }
        .preferredColorScheme(themeManager.currentTheme.colorScheme)
    }
}

#Preview {
    MainView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
        .environmentObject(ProductViewModel(
            repository: ProductRepository(context: PersistenceController.preview.container.viewContext),
            categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(AnalyticsViewModel(
            productRepository: ProductRepository(context: PersistenceController.preview.container.viewContext),
            categoryRepository: CategoryRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(TagViewModel(
            repository: TagRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(PurchaseAnalyticsViewModel(
            productRepository: ProductRepository(context: PersistenceController.preview.container.viewContext)
        ))
        .environmentObject(SimulationViewModel())
        .environmentObject(UsageViewModel(
            usageRepository: UsageRecordRepository(context: PersistenceController.preview.container.viewContext),
            expenseRepository: RelatedExpenseRepository(context: PersistenceController.preview.container.viewContext)
        ))
        // 移除了 LoanRecordViewModel
        .environmentObject(ThemeManager())
}
