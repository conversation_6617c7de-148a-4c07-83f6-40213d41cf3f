//
//  pinklogApp.swift
//  pinklog
//
//  Created by thr33 on 2025/5/10.
//

import SwiftUI

@main
struct pinklogApp: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var themeManager = ThemeManager() // 添加 ThemeManager
    @StateObject private var purchaseChannelViewModel: PurchaseChannelViewModel
    @StateObject private var timelineViewModel: TimelineViewModel

    init() {
        // 初始化视图上下文
        let viewContext = PersistenceController.shared.container.viewContext

        // 初始化PurchaseChannelViewModel
        _purchaseChannelViewModel = StateObject(wrappedValue: PurchaseChannelViewModel(context: viewContext))

        // 初始化TimelineViewModel
        let usageRepository = UsageRecordRepository(context: viewContext)
        let expenseRepository = RelatedExpenseRepository(context: viewContext)
        _timelineViewModel = StateObject(wrappedValue: TimelineViewModel(
            usageRepository: usageRepository,
            expenseRepository: expenseRepository
        ))

        // 修改NavigationLink箭头的全局样式
        UINavigationBar.appearance().tintColor = UIColor(Color.accentColor) // 注意：这里的 AccentColor 可能也需要根据主题动态变化
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(themeManager) // 注入 ThemeManager
                .environmentObject(purchaseChannelViewModel) // 注入 PurchaseChannelViewModel
                .environmentObject(timelineViewModel) // 注入 TimelineViewModel
                .preferredColorScheme(themeManager.currentTheme.colorScheme) // 应用 preferredColorScheme
                .onAppear {
                    // 启动时清理缓存和监控内存
                    print("🚀 应用启动，初始内存: \(String(format: "%.1f", MemoryMonitor.shared.getCurrentMemoryUsage()))MB")
                    LazyLoadingManager.shared.clearCache()

                    // 在App启动时执行初始化任务
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        // 迁移旧的字符串渠道到新的关系模型
                        purchaseChannelViewModel.migrateAllProducts()

                        // 初始化渐进式JPEG设置
                        if UserDefaults.standard.object(forKey: "useProgressiveJPEG") == nil {
                            // 默认启用渐进式JPEG
                            UserDefaults.standard.set(true, forKey: "useProgressiveJPEG")
                        }

                        // 启动后内存检查
                        MemoryMonitor.shared.checkMemoryUsage()
                    }
                }
        }
    }
}
